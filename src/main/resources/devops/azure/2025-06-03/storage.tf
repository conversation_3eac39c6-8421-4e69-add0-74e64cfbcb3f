resource "azurerm_storage_account" "middleware_storage_account" {
  count = var.storage_enabled ? 1 : 0
  name                     = "${replace(var.tenant, "-", "0")}0middleware0${var.profile}"
  resource_group_name      = azurerm_resource_group.middleware_resource_group[count.index].name
  location                 = azurerm_resource_group.middleware_resource_group[count.index].location
  account_tier             = "Standard"
  account_replication_type = "LRS"
}

resource "azurerm_storage_container" "middleware_storage_container" {
  count = var.storage_enabled ? 1 : 0
  name                  = "${var.tenant}-middleware-storage-container-${var.profile}"
  storage_account_id  = azurerm_storage_account.middleware_storage_account[count.index].id
  container_access_type = "blob"
}

resource "azurerm_storage_blob" "middleware_storage_blob_jar" {
  count = var.storage_enabled ? 1 : 0
  name                   = "${var.release}.jar"
  storage_account_name   = azurerm_storage_account.middleware_storage_account[count.index].name
  storage_container_name = azurerm_storage_container.middleware_storage_container[count.index].name
  type                   = "Block"
  source                 = "${path.cwd}/../../../../../build/libs/middleware.jar"
}