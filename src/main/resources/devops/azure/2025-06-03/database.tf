locals {
  database_username = "sqladminuser"
  database_password = "Strong<PERSON>@ssw0rd!"
}

resource "azurerm_mssql_server" "middleware_sqlserver" {
  count = var.database_enabled ? 1 : 0
  name                         = "${var.tenant}-middleware-sqlserver-${var.profile}"
  resource_group_name          = azurerm_resource_group.middleware_resource_group[count.index].name
  location                     = azurerm_resource_group.middleware_resource_group[count.index].location
  version                      = "12.0"
  administrator_login          = local.database_username
  administrator_login_password = local.database_password
  public_network_access_enabled = true
}

resource "azurerm_mssql_database" "middleware_database" {
  count = var.database_enabled ? 1 : 0
  name                    = "${var.tenant}-middleware-database-${var.profile}"
  server_id               = azurerm_mssql_server.middleware_sqlserver[count.index].id
  sku_name                = "GP_S_Gen5_1"
  max_size_gb             = 1
  zone_redundant          = false
  auto_pause_delay_in_minutes = var.database_pause_after_minutes
  min_capacity               = 0.5
  read_scale                 = false
  short_term_retention_policy {
    # Point-in-time backup for quick recovery
    retention_days = var.backup_period_days
    backup_interval_in_hours = 24
  }
  long_term_retention_policy {
    # Enabling long-term backup retention for a serverless database is not supported if auto-pause is enabled.
    monthly_retention = var.database_pause_after_minutes == -1 ? "P12M" : "PT0S"
  }
}

resource "azurerm_mssql_firewall_rule" "allow_middleware_database_access" {
  count = var.database_enabled ? 1 : 0
  name                = "${var.tenant}-middleware-access-${var.profile}"
  server_id           = azurerm_mssql_server.middleware_sqlserver[count.index].id
  start_ip_address    = "0.0.0.0"
  end_ip_address      = "***************"
}