terraform {
  required_providers {
    azurerm = {
      source = "hashicorp/azurerm"
      version = "4.32.0"
    }
    azuread = {
      source = "hashicorp/azuread"
      version = "3.4.0"
    }
  }
}

provider "azurerm" {
  features {}
  subscription_id = var.subscription_id
}

resource "azurerm_resource_group" "middleware_resource_group" {
  count = var.database_enabled || var.service_enabled ? 1 : 0
  name     = "${var.tenant}-middleware-resource-group-${var.profile}"
  location = "East Asia"
}