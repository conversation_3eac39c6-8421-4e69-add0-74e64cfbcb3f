variable "profile" {
  type = string
}

variable "tenant" {
  type = string
}

variable "release" {
  type = string
}

variable "database_enabled" {
  type = bool
  default = true
}

variable "storage_enabled" {
  type = bool
  default = true
}

variable "service_enabled" {
  type = bool
  default = true
}

variable "service_active" {
  type = bool
  default = true
}

variable "database_pause_after_minutes" {
  type = number
  default = 15
}

variable "backup_period_days" {
  type = number
}

variable "subscription_id" {
  type = string
}