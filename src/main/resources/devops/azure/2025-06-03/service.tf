resource "azurerm_service_plan" "middleware_service_plan" {
  count = var.service_enabled ? 1 : 0
  name                = "${var.tenant}-middleware-service-plan-${var.profile}"
  location            = azurerm_resource_group.middleware_resource_group[count.index].location
  resource_group_name = azurerm_resource_group.middleware_resource_group[count.index].name
  os_type             = "Linux"
  sku_name            = "B2"
}

resource "azurerm_linux_web_app" "middleware_linux_web_app" {
  count = var.service_enabled ? 1 : 0
  enabled = var.service_active
  name                = "${var.tenant}-middleware-linux-web-app-${var.profile}"
  location            = azurerm_resource_group.middleware_resource_group[count.index].location
  resource_group_name = azurerm_resource_group.middleware_resource_group[count.index].name
  service_plan_id     = azurerm_service_plan.middleware_service_plan[count.index].id

  site_config {
    app_command_line = "java -Dfile.encoding=UTF-8 -Dspring.profiles.active=sit -Dspring.datasource.jdbc-url=jdbc:sqlserver://${azurerm_mssql_server.middleware_sqlserver[count.index].name}.database.windows.net:1433;databaseName=${azurerm_mssql_database.middleware_database[count.index].name};encrypt=false;sendStringParametersAsUnicode=true -Dspring.secondary-datasource.jdbc-url=jdbc:sqlserver://${azurerm_mssql_server.middleware_sqlserver[count.index].name}.database.windows.net:1433;databaseName=${azurerm_mssql_database.middleware_database[count.index].name};encrypt=false;sendStringParametersAsUnicode=true -Dspring.som-datasource.jdbc-url=jdbc:sqlserver://${azurerm_mssql_server.middleware_sqlserver[count.index].name}.database.windows.net:1433;databaseName=${azurerm_mssql_database.middleware_database[count.index].name};encrypt=false;sendStringParametersAsUnicode=true -cp /home/<USER>/${var.release}.jar com.asw.middleware.MiddlewareApplication"
    application_stack {
      java_server         = "JAVA"
      java_server_version = "17"
      java_version        = "17"
    }
  }

  storage_account {
    access_key   = azurerm_storage_account.middleware_storage_account[count.index].primary_access_key
    account_name = azurerm_storage_account.middleware_storage_account[count.index].name
    name         = azurerm_storage_account.middleware_storage_account[count.index].name
    share_name   = azurerm_storage_container.middleware_storage_container[count.index].name
    type         = "AzureBlob"
    mount_path   = "/home/<USER>"
  }

  app_settings = {
    "WEBSITES_PORT"          = "80"
    "WEBSITES_ENABLE_APP_SERVICE_STORAGE" = "false"
    "SPRING_DATASOURCE_USERNAME" = local.database_username
    "SPRING_DATASOURCE_PASSWORD" = local.database_password
    "SPRING_SECONDARY_DATASOURCE_USERNAME" = local.database_username
    "SPRING_SECONDARY_DATASOURCE_PASSWORD" = local.database_password
    "SPRING_SOM_DATASOURCE_USERNAME" = local.database_username
    "SPRING_SOM_DATASOURCE_PASSWORD" = local.database_password
  }

  https_only = true
}
