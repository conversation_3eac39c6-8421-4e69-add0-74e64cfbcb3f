{"version": 4, "terraform_version": "1.5.7", "serial": 137, "lineage": "5df22cf6-ec31-d434-5803-10ef210fcfc4", "outputs": {}, "resources": [{"module": "module.asw_water_middleware_sit", "mode": "managed", "type": "azurerm_linux_web_app", "name": "middleware_linux_web_app", "provider": "module.asw_water_middleware_sit.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"app_settings": {"SPRING_DATASOURCE_PASSWORD": "StrongP@ssw0rd!", "SPRING_DATASOURCE_USERNAME": "sqladminuser", "SPRING_SECONDARY_DATASOURCE_PASSWORD": "StrongP@ssw0rd!", "SPRING_SECONDARY_DATASOURCE_USERNAME": "sqladminuser", "SPRING_SOM_DATASOURCE_PASSWORD": "StrongP@ssw0rd!", "SPRING_SOM_DATASOURCE_USERNAME": "sqladminuser", "WEBSITES_ENABLE_APP_SERVICE_STORAGE": "false", "WEBSITES_PORT": "80"}, "auth_settings": [], "auth_settings_v2": [], "backup": [], "client_affinity_enabled": false, "client_certificate_enabled": false, "client_certificate_exclusion_paths": "", "client_certificate_mode": "Required", "connection_string": [], "custom_domain_verification_id": "559B54B821F6AFC89173319771CD415E33DEF3B0132CD4D10A5A0117DD086B65", "default_hostname": "asw-water-middleware-linux-web-app-sit.azurewebsites.net", "enabled": true, "ftp_publish_basic_authentication_enabled": true, "hosting_environment_id": "", "https_only": true, "id": "/subscriptions/afe919cd-c103-4e4d-8f19-3375892c2d53/resourceGroups/asw-water-middleware-resource-group-sit/providers/Microsoft.Web/sites/asw-water-middleware-linux-web-app-sit", "identity": [], "key_vault_reference_identity_id": "SystemAssigned", "kind": "app,linux", "location": "eastasia", "logs": [], "name": "asw-water-middleware-linux-web-app-sit", "outbound_ip_address_list": ["************", "************", "************", "**********", "************"], "outbound_ip_addresses": "************,************,************,**********,************", "possible_outbound_ip_address_list": ["************", "************", "************", "**********", "************", "************", "*************", "*************", "************", "*************", "**************", "**************", "**************", "*************", "*************", "************", "************", "************", "*************", "*************", "*************", "************"], "possible_outbound_ip_addresses": "************,************,************,**********,************,************,*************,*************,************,*************,**************,**************,**************,*************,*************,************,************,************,*************,*************,*************,************", "public_network_access_enabled": true, "resource_group_name": "asw-water-middleware-resource-group-sit", "service_plan_id": "/subscriptions/afe919cd-c103-4e4d-8f19-3375892c2d53/resourceGroups/asw-water-middleware-resource-group-sit/providers/Microsoft.Web/serverFarms/asw-water-middleware-service-plan-sit", "site_config": [{"always_on": true, "api_definition_url": "", "api_management_api_id": "", "app_command_line": "java -Dfile.encoding=UTF-8 -Dspring.profiles.active=sit -Dspring.datasource.jdbc-url=***************************************************************************************************************************************************************************** -Dspring.secondary-datasource.jdbc-url=***************************************************************************************************************************************************************************** -Dspring.som-datasource.jdbc-url=***************************************************************************************************************************************************************************** -cp /home/<USER>/2025.***********.jar com.asw.middleware.MiddlewareApplication", "application_stack": [{"docker_image_name": "", "docker_registry_password": "", "docker_registry_url": "", "docker_registry_username": "", "dotnet_version": "", "go_version": "", "java_server": "JAVA", "java_server_version": "17", "java_version": "17", "node_version": "", "php_version": "", "python_version": "", "ruby_version": ""}], "auto_heal_setting": [], "container_registry_managed_identity_client_id": "", "container_registry_use_managed_identity": false, "cors": [], "default_documents": ["Default.htm", "Default.html", "Default.asp", "index.htm", "index.html", "iisstart.htm", "default.aspx", "index.php", "hostingstart.html"], "detailed_error_logging_enabled": false, "ftps_state": "Disabled", "health_check_eviction_time_in_min": 0, "health_check_path": "", "http2_enabled": false, "ip_restriction": [], "ip_restriction_default_action": "Allow", "linux_fx_version": "JAVA|17-java17", "load_balancing_mode": "LeastRequests", "local_mysql_enabled": false, "managed_pipeline_mode": "Integrated", "minimum_tls_version": "1.2", "remote_debugging_enabled": false, "remote_debugging_version": "VS2022", "scm_ip_restriction": [], "scm_ip_restriction_default_action": "Allow", "scm_minimum_tls_version": "1.2", "scm_type": "None", "scm_use_main_ip_restriction": false, "use_32_bit_worker": true, "vnet_route_all_enabled": false, "websockets_enabled": false, "worker_count": 1}], "site_credential": [{"name": "$asw-water-middleware-linux-web-app-sit", "password": "ZButk6FYgjZGvv0P7Fw0yaT68Benk17qegF63xB3rdpkGRQlL86l94vMhd8e"}], "sticky_settings": [], "storage_account": [{"access_key": "****************************************************************************************", "account_name": "asw0water0middleware0sit", "mount_path": "/home/<USER>", "name": "asw0water0middleware0sit", "share_name": "asw-water-middleware-storage-container-sit", "type": "AzureBlob"}], "tags": null, "timeouts": null, "virtual_network_backup_restore_enabled": false, "virtual_network_subnet_id": "", "webdeploy_publish_basic_authentication_enabled": true, "zip_deploy_file": ""}, "sensitive_attributes": [[{"type": "get_attr", "value": "storage_account"}]], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["module.asw_water_middleware_sit.azurerm_mssql_database.middleware_database", "module.asw_water_middleware_sit.azurerm_mssql_server.middleware_sqlserver", "module.asw_water_middleware_sit.azurerm_resource_group.middleware_resource_group", "module.asw_water_middleware_sit.azurerm_service_plan.middleware_service_plan", "module.asw_water_middleware_sit.azurerm_storage_account.middleware_storage_account", "module.asw_water_middleware_sit.azurerm_storage_container.middleware_storage_container"]}]}, {"module": "module.asw_water_middleware_sit", "mode": "managed", "type": "azurerm_mssql_database", "name": "middleware_database", "provider": "module.asw_water_middleware_sit.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"auto_pause_delay_in_minutes": 15, "collation": "SQL_Latin1_General_CP1_CI_AS", "create_mode": "<PERSON><PERSON><PERSON>", "creation_source_database_id": null, "elastic_pool_id": "", "enclave_type": "", "geo_backup_enabled": true, "id": "/subscriptions/afe919cd-c103-4e4d-8f19-3375892c2d53/resourceGroups/asw-water-middleware-resource-group-sit/providers/Microsoft.Sql/servers/asw-water-middleware-sqlserver-sit/databases/asw-water-middleware-database-sit", "identity": [], "import": [], "ledger_enabled": false, "license_type": "", "long_term_retention_policy": [{"immutable_backups_enabled": false, "monthly_retention": "PT0S", "week_of_year": 1, "weekly_retention": "PT0S", "yearly_retention": "PT0S"}], "maintenance_configuration_name": "SQL_Default", "max_size_gb": 1, "min_capacity": 0.5, "name": "asw-water-middleware-database-sit", "read_replica_count": 0, "read_scale": false, "recover_database_id": null, "recovery_point_id": null, "restore_dropped_database_id": null, "restore_long_term_retention_backup_id": null, "restore_point_in_time": null, "sample_name": null, "secondary_type": "", "server_id": "/subscriptions/afe919cd-c103-4e4d-8f19-3375892c2d53/resourceGroups/asw-water-middleware-resource-group-sit/providers/Microsoft.Sql/servers/asw-water-middleware-sqlserver-sit", "short_term_retention_policy": [{"backup_interval_in_hours": 24, "retention_days": 3}], "sku_name": "GP_S_Gen5_1", "storage_account_type": "Geo", "tags": {}, "threat_detection_policy": [{"disabled_alerts": [], "email_account_admins": "Disabled", "email_addresses": [], "retention_days": 0, "state": "Disabled", "storage_account_access_key": "", "storage_endpoint": ""}], "timeouts": null, "transparent_data_encryption_enabled": true, "transparent_data_encryption_key_automatic_rotation_enabled": false, "transparent_data_encryption_key_vault_key_id": "", "zone_redundant": false}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["module.asw_water_middleware_sit.azurerm_mssql_server.middleware_sqlserver", "module.asw_water_middleware_sit.azurerm_resource_group.middleware_resource_group"]}]}, {"module": "module.asw_water_middleware_sit", "mode": "managed", "type": "azurerm_mssql_firewall_rule", "name": "allow_middleware_database_access", "provider": "module.asw_water_middleware_sit.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"end_ip_address": "***************", "id": "/subscriptions/afe919cd-c103-4e4d-8f19-3375892c2d53/resourceGroups/asw-water-middleware-resource-group-sit/providers/Microsoft.Sql/servers/asw-water-middleware-sqlserver-sit/firewallRules/asw-water-middleware-access-sit", "name": "asw-water-middleware-access-sit", "server_id": "/subscriptions/afe919cd-c103-4e4d-8f19-3375892c2d53/resourceGroups/asw-water-middleware-resource-group-sit/providers/Microsoft.Sql/servers/asw-water-middleware-sqlserver-sit", "start_ip_address": "0.0.0.0", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjoxODAwMDAwMDAwMDAwfX0=", "dependencies": ["module.asw_water_middleware_sit.azurerm_mssql_server.middleware_sqlserver", "module.asw_water_middleware_sit.azurerm_resource_group.middleware_resource_group"]}]}, {"module": "module.asw_water_middleware_sit", "mode": "managed", "type": "azurerm_mssql_server", "name": "middleware_sqlserver", "provider": "module.asw_water_middleware_sit.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"administrator_login": "sqladminuser", "administrator_login_password": "StrongP@ssw0rd!", "administrator_login_password_wo": null, "administrator_login_password_wo_version": 0, "azuread_administrator": [], "connection_policy": "<PERSON><PERSON><PERSON>", "express_vulnerability_assessment_enabled": false, "fully_qualified_domain_name": "asw-water-middleware-sqlserver-sit.database.windows.net", "id": "/subscriptions/afe919cd-c103-4e4d-8f19-3375892c2d53/resourceGroups/asw-water-middleware-resource-group-sit/providers/Microsoft.Sql/servers/asw-water-middleware-sqlserver-sit", "identity": [], "location": "eastasia", "minimum_tls_version": "1.2", "name": "asw-water-middleware-sqlserver-sit", "outbound_network_restriction_enabled": false, "primary_user_assigned_identity_id": "", "public_network_access_enabled": true, "resource_group_name": "asw-water-middleware-resource-group-sit", "restorable_dropped_database_ids": [], "tags": {}, "timeouts": null, "transparent_data_encryption_key_vault_key_id": "", "version": "12.0"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozNjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjozNjAwMDAwMDAwMDAwfX0=", "dependencies": ["module.asw_water_middleware_sit.azurerm_resource_group.middleware_resource_group"]}]}, {"module": "module.asw_water_middleware_sit", "mode": "managed", "type": "azurerm_resource_group", "name": "middleware_resource_group", "provider": "module.asw_water_middleware_sit.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "/subscriptions/afe919cd-c103-4e4d-8f19-3375892c2d53/resourceGroups/asw-water-middleware-resource-group-sit", "location": "eastasia", "managed_by": "", "name": "asw-water-middleware-resource-group-sit", "tags": {}, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo1NDAwMDAwMDAwMDAwLCJkZWxldGUiOjU0MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjo1NDAwMDAwMDAwMDAwfX0="}]}, {"module": "module.asw_water_middleware_sit", "mode": "managed", "type": "azurerm_service_plan", "name": "middleware_service_plan", "provider": "module.asw_water_middleware_sit.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"app_service_environment_id": "", "id": "/subscriptions/afe919cd-c103-4e4d-8f19-3375892c2d53/resourceGroups/asw-water-middleware-resource-group-sit/providers/Microsoft.Web/serverFarms/asw-water-middleware-service-plan-sit", "kind": "linux", "location": "eastasia", "maximum_elastic_worker_count": 1, "name": "asw-water-middleware-service-plan-sit", "os_type": "Linux", "per_site_scaling_enabled": false, "premium_plan_auto_scale_enabled": false, "reserved": true, "resource_group_name": "asw-water-middleware-resource-group-sit", "sku_name": "B2", "tags": null, "timeouts": null, "worker_count": 1, "zone_balancing_enabled": false}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["module.asw_water_middleware_sit.azurerm_resource_group.middleware_resource_group"]}]}, {"module": "module.asw_water_middleware_sit", "mode": "managed", "type": "azurerm_storage_account", "name": "middleware_storage_account", "provider": "module.asw_water_middleware_sit.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 4, "attributes": {"access_tier": "Hot", "account_kind": "StorageV2", "account_replication_type": "LRS", "account_tier": "Standard", "allow_nested_items_to_be_public": true, "allowed_copy_scope": "", "azure_files_authentication": [], "blob_properties": [{"change_feed_enabled": false, "change_feed_retention_in_days": 0, "container_delete_retention_policy": [], "cors_rule": [], "default_service_version": "", "delete_retention_policy": [], "last_access_time_enabled": false, "restore_policy": [], "versioning_enabled": false}], "cross_tenant_replication_enabled": false, "custom_domain": [], "customer_managed_key": [], "default_to_oauth_authentication": false, "dns_endpoint_type": "Standard", "edge_zone": "", "https_traffic_only_enabled": true, "id": "/subscriptions/afe919cd-c103-4e4d-8f19-3375892c2d53/resourceGroups/asw-water-middleware-resource-group-sit/providers/Microsoft.Storage/storageAccounts/asw0water0middleware0sit", "identity": [], "immutability_policy": [], "infrastructure_encryption_enabled": false, "is_hns_enabled": false, "large_file_share_enabled": false, "local_user_enabled": true, "location": "eastasia", "min_tls_version": "TLS1_2", "name": "asw0water0middleware0sit", "network_rules": [], "nfsv3_enabled": false, "primary_access_key": "****************************************************************************************", "primary_blob_connection_string": "DefaultEndpointsProtocol=https;BlobEndpoint=https://asw0water0middleware0sit.blob.core.windows.net/;AccountName=asw0water0middleware0sit;AccountKey=****************************************************************************************", "primary_blob_endpoint": "https://asw0water0middleware0sit.blob.core.windows.net/", "primary_blob_host": "asw0water0middleware0sit.blob.core.windows.net", "primary_blob_internet_endpoint": "", "primary_blob_internet_host": "", "primary_blob_microsoft_endpoint": "", "primary_blob_microsoft_host": "", "primary_connection_string": "DefaultEndpointsProtocol=https;AccountName=asw0water0middleware0sit;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "primary_dfs_endpoint": "https://asw0water0middleware0sit.dfs.core.windows.net/", "primary_dfs_host": "asw0water0middleware0sit.dfs.core.windows.net", "primary_dfs_internet_endpoint": "", "primary_dfs_internet_host": "", "primary_dfs_microsoft_endpoint": "", "primary_dfs_microsoft_host": "", "primary_file_endpoint": "https://asw0water0middleware0sit.file.core.windows.net/", "primary_file_host": "asw0water0middleware0sit.file.core.windows.net", "primary_file_internet_endpoint": "", "primary_file_internet_host": "", "primary_file_microsoft_endpoint": "", "primary_file_microsoft_host": "", "primary_location": "eastasia", "primary_queue_endpoint": "https://asw0water0middleware0sit.queue.core.windows.net/", "primary_queue_host": "asw0water0middleware0sit.queue.core.windows.net", "primary_queue_microsoft_endpoint": "", "primary_queue_microsoft_host": "", "primary_table_endpoint": "https://asw0water0middleware0sit.table.core.windows.net/", "primary_table_host": "asw0water0middleware0sit.table.core.windows.net", "primary_table_microsoft_endpoint": "", "primary_table_microsoft_host": "", "primary_web_endpoint": "https://asw0water0middleware0sit.z7.web.core.windows.net/", "primary_web_host": "asw0water0middleware0sit.z7.web.core.windows.net", "primary_web_internet_endpoint": "", "primary_web_internet_host": "", "primary_web_microsoft_endpoint": "", "primary_web_microsoft_host": "", "public_network_access_enabled": true, "queue_encryption_key_type": "Service", "queue_properties": [{"cors_rule": [], "hour_metrics": [{"enabled": false, "include_apis": false, "retention_policy_days": 0, "version": "1.0"}], "logging": [{"delete": false, "read": false, "retention_policy_days": 0, "version": "1.0", "write": false}], "minute_metrics": [{"enabled": false, "include_apis": false, "retention_policy_days": 0, "version": "1.0"}]}], "resource_group_name": "asw-water-middleware-resource-group-sit", "routing": [], "sas_policy": [], "secondary_access_key": "****************************************************************************************", "secondary_blob_connection_string": "", "secondary_blob_endpoint": "", "secondary_blob_host": "", "secondary_blob_internet_endpoint": "", "secondary_blob_internet_host": "", "secondary_blob_microsoft_endpoint": "", "secondary_blob_microsoft_host": "", "secondary_connection_string": "DefaultEndpointsProtocol=https;AccountName=asw0water0middleware0sit;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "secondary_dfs_endpoint": "", "secondary_dfs_host": "", "secondary_dfs_internet_endpoint": "", "secondary_dfs_internet_host": "", "secondary_dfs_microsoft_endpoint": "", "secondary_dfs_microsoft_host": "", "secondary_file_endpoint": "", "secondary_file_host": "", "secondary_file_internet_endpoint": "", "secondary_file_internet_host": "", "secondary_file_microsoft_endpoint": "", "secondary_file_microsoft_host": "", "secondary_location": "", "secondary_queue_endpoint": "", "secondary_queue_host": "", "secondary_queue_microsoft_endpoint": "", "secondary_queue_microsoft_host": "", "secondary_table_endpoint": "", "secondary_table_host": "", "secondary_table_microsoft_endpoint": "", "secondary_table_microsoft_host": "", "secondary_web_endpoint": "", "secondary_web_host": "", "secondary_web_internet_endpoint": "", "secondary_web_internet_host": "", "secondary_web_microsoft_endpoint": "", "secondary_web_microsoft_host": "", "sftp_enabled": false, "share_properties": [{"cors_rule": [], "retention_policy": [{"days": 7}], "smb": []}], "shared_access_key_enabled": true, "static_website": [], "table_encryption_key_type": "Service", "tags": {}, "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozNjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjozNjAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiI0In0=", "dependencies": ["module.asw_water_middleware_sit.azurerm_resource_group.middleware_resource_group"]}]}, {"module": "module.asw_water_middleware_sit", "mode": "managed", "type": "azurerm_storage_blob", "name": "middleware_storage_blob_jar", "provider": "module.asw_water_middleware_sit.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"access_tier": "Hot", "cache_control": "", "content_md5": "", "content_type": "application/octet-stream", "encryption_scope": "", "id": "https://asw0water0middleware0sit.blob.core.windows.net/asw-water-middleware-storage-container-sit/2025.***********.jar", "metadata": {}, "name": "2025.***********.jar", "parallelism": 8, "size": 0, "source": "/Users/<USER>/src/github.com/asw/middleware/src/main/resources/devops/azure/../../../../../build/libs/middleware.jar", "source_content": null, "source_uri": null, "storage_account_name": "asw0water0middleware0sit", "storage_container_name": "asw-water-middleware-storage-container-sit", "timeouts": null, "type": "Block", "url": "https://asw0water0middleware0sit.blob.core.windows.net/asw-water-middleware-storage-container-sit/2025.***********.jar"}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["module.asw_water_middleware_sit.azurerm_resource_group.middleware_resource_group", "module.asw_water_middleware_sit.azurerm_storage_account.middleware_storage_account", "module.asw_water_middleware_sit.azurerm_storage_container.middleware_storage_container"]}]}, {"module": "module.asw_water_middleware_sit", "mode": "managed", "type": "azurerm_storage_container", "name": "middleware_storage_container", "provider": "module.asw_water_middleware_sit.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"container_access_type": "blob", "default_encryption_scope": "$account-encryption-key", "encryption_scope_override_enabled": true, "has_immutability_policy": false, "has_legal_hold": false, "id": "/subscriptions/afe919cd-c103-4e4d-8f19-3375892c2d53/resourceGroups/asw-water-middleware-resource-group-sit/providers/Microsoft.Storage/storageAccounts/asw0water0middleware0sit/blobServices/default/containers/asw-water-middleware-storage-container-sit", "metadata": {}, "name": "asw-water-middleware-storage-container-sit", "resource_manager_id": "/subscriptions/afe919cd-c103-4e4d-8f19-3375892c2d53/resourceGroups/asw-water-middleware-resource-group-sit/providers/Microsoft.Storage/storageAccounts/asw0water0middleware0sit/blobServices/default/containers/asw-water-middleware-storage-container-sit", "storage_account_id": "/subscriptions/afe919cd-c103-4e4d-8f19-3375892c2d53/resourceGroups/asw-water-middleware-resource-group-sit/providers/Microsoft.Storage/storageAccounts/asw0water0middleware0sit", "storage_account_name": "", "timeouts": null}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["module.asw_water_middleware_sit.azurerm_resource_group.middleware_resource_group", "module.asw_water_middleware_sit.azurerm_storage_account.middleware_storage_account"]}]}], "check_results": null}