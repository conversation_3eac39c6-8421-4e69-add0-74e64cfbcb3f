CREATE TABLE header
(
    bu_code       VARCHAR(50) PRIMARY KEY,
    extdate_time  DATETIME,
    from_location VARCHAR(255),
    to_location   VARCHAR(255)
);

CREATE TABLE [user]
(
    code            VARCHAR(50) PRIMARY KEY,
    description     TEXT,
    vat_code        VARCHAR(50),
    fiscal_code     VARCHAR(50),
    loyalty_card_id VARCHAR(50),
    card_number     VARCHAR(50),
    account_no      VARCHAR(50)
);

CREATE TABLE shipping_info
(
    id             VARCHAR(50) PRIMARY KEY,
    personal_title VARCHAR(50),
    first_name     VARCHAR(100),
    last_name      VARCHAR(100),
    address_line   TEXT,
    town           VARCHAR(100),
    district       VARCHAR(100),
    country        VARCHAR(100),
    phone1         VARCHAR(50),
    latitude       DECIMAL(9, 6),
    longitude      DECIMAL(9, 6)
);

CREATE TABLE billing_info
(
    id             VARCHAR(50) PRIMARY KEY,
    personal_title VARCHAR(50),
    first_name     VARCHA<PERSON>(100),
    last_name      VARCHAR(100),
    address_line   TEXT,
    town           VARCHAR(100),
    district       VARCHAR(100),
    country        VARCHAR(100),
    phone1         VARCHAR(50)
);

CREATE TABLE payments
(
    id                VARCHAR(50) PRIMARY KEY,
    mode              VARCHAR(50),
    type              VARCHAR(50),
    cost              DECIMAL(10, 2),
    amount            DECIMAL(10, 2),
    payment_reference VARCHAR(255),
    payment_provider  VARCHAR(100),
    auth_date_time    DATETIME,
    capture_date_time DATETIME
);

CREATE TABLE [order]
(
    id                               VARCHAR(50) PRIMARY KEY,
    bu_code                          VARCHAR(50),
    creation_time                    DATETIME,
    cons_code                        VARCHAR(50),
    warehouse                        VARCHAR(100),
    update_time                      DATETIME,
    user_code                        VARCHAR(50),
    total_price                      DECIMAL(10, 2),
    total_discounts                  DECIMAL(10, 2),
    total_tax                        DECIMAL(10, 2),
    delivery_cost                    DECIMAL(10, 2),
    currency                         VARCHAR(50),
    status                           VARCHAR(50),
    consignment_status               VARCHAR(50),
    payment_status                   VARCHAR(50),
    delivery_mode                    VARCHAR(50),
    email                            VARCHAR(255),
    shipping_info_id                 VARCHAR(50),
    billing_info_id                  VARCHAR(50),
    payments_id                      VARCHAR(50),
    reference_ex_rate                DECIMAL(10, 5),
    is_milk_powder                   BIT,
    post_code                        VARCHAR(50),
    ecno                             VARCHAR(50),
    odno                             VARCHAR(50),
    amt                              DECIMAL(10, 2),
    prodmn                           VARCHAR(100),
    realmt                           VARCHAR(100),
    trade_type                       VARCHAR(50),
    ser_code                         VARCHAR(50),
    ecndo                            VARCHAR(50),
    service_charge                   DECIMAL(10, 2),
    dead_line                        DATETIME,
    original_order_id                VARCHAR(50),
    original_creation_time           DATETIME,
    special_instruction              TEXT,
    delivery_recycle_collect_request BIT,
    pack_type                        VARCHAR(50),
    delivery_date                    DATE,
    delivery_time_slot               VARCHAR(50),
    show_price                       BIT,
    invoice_file_name                VARCHAR(255),
    pick_type                        VARCHAR(50),
    shipping_location                VARCHAR(255),
    cold_bag_needed                  BIT,
    parent_order_id                  VARCHAR(50),
    child_order_id                   VARCHAR(50),
    created_on                       datetime2,
    created_by_xml                   VARCHAR(50),
    updated_on                       datetime2,

    FOREIGN KEY (bu_code) REFERENCES [header] (bu_code),
    FOREIGN KEY (user_code) REFERENCES [user] (code),
    FOREIGN KEY (shipping_info_id) REFERENCES shipping_info (id),
    FOREIGN KEY (billing_info_id) REFERENCES billing_info (id),
    FOREIGN KEY (payments_id) REFERENCES payments (id)
);

CREATE TABLE consignments
(
    id                   VARCHAR(50) PRIMARY KEY,
    cons_code            VARCHAR(50),
    warehouse            VARCHAR(100),
    consignment_status   VARCHAR(50),
    type_of_distribution VARCHAR(50),
    delivery_date        DATE,
    delivery_time_slot   VARCHAR(50),
    order_id             VARCHAR(50),
    FOREIGN KEY (order_id) REFERENCES [order] (id)
);

CREATE TABLE order_entries
(
    id              VARCHAR(50) PRIMARY KEY,
    sku_id          VARCHAR(50),
    product_id      VARCHAR(50),
    quantity        VARCHAR(50),
    sales_unit      VARCHAR(50),
    base_price      DECIMAL(10, 2),
    discount        DECIMAL(10, 2),
    giveaway        BIT,
    total_price     DECIMAL(10, 2),
    tax_amount      DECIMAL(10, 2),
    sequence_number INT,
    product_name    VARCHAR(255),
    sku_type        VARCHAR(50),
    brand           VARCHAR(100),
    size_desc       VARCHAR(100),
    supplier_code   VARCHAR(50),
    donate_qty      INT,
    consignment_id  VARCHAR(50),
    FOREIGN KEY (consignment_id) REFERENCES consignments (id)
);

CREATE TABLE customer_template
(
    account_no           VARCHAR(50) PRIMARY KEY,
    from_order_id        VARCHAR(50),
    customer_id          VARCHAR(50) ,
    modified_on          VARCHAR(50),
    name                 TEXT,
    long_name            TEXT,
    code_marketing_group VARCHAR(50),
    first_name           TEXT,
    phone                VARCHAR(50),
    owner                TEXT,
    owner_business_unit  TEXT,
    email                VARCHAR(255),
    street               VARCHAR(255),
    building             VARCHAR(255),
    district             VARCHAR(100),
    fax                  VARCHAR(50),
    additional           TEXT,
    created_on           datetime2,
    updated_on           datetime2,
)

CREATE TABLE account_template
(
    account_no      VARCHAR(50) PRIMARY KEY,
    from_order_id   VARCHAR(50),
    main_phone      VARCHAR(50),
    address1        TEXT,
    primary_contact TEXT,
    email           VARCHAR(255),
    status          VARCHAR(50),
    created_on      datetime2,
    updated_on      datetime2,
)

CREATE TABLE address
(
    external_id  VARCHAR(50) PRIMARY KEY,
    type         VARCHAR(50), -- district, area, street
    name         VARCHAR(255),
    chinese_name VARCHAR(255),
    updated_at   datetime2
);

CREATE TABLE building
(
    id           VARCHAR(50) PRIMARY KEY,
    name         VARCHAR(255),
    chinese_name VARCHAR(255),
    street_no    VARCHAR(50),
    street_id    VARCHAR(50), -- mapping to external_id of address table
    district_id  VARCHAR(50), -- mapping to external_id of address table
    area_id      VARCHAR(50), -- mapping to external_id of address table
    updated_at   datetime2
);
