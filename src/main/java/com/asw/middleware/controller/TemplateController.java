package com.asw.middleware.controller;

import com.asw.middleware.model.middleware.TemplateConfig;
import com.asw.middleware.service.TemplateConfigService;
import com.asw.middleware.service.TemplateService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for handling template-related operations
 */
@RestController
@RequestMapping("/api/v1/templates")
@RequiredArgsConstructor
@Slf4j
public class TemplateController {

    private final TemplateService templateService;
    private final TemplateConfigService templateConfigService;

    @GetMapping
    public ResponseEntity<Resource> downloadCustomerTemplates() {
        Resource resource = templateService.downloadCustomerTemplates(false);
        String fileName = "customer_templates.xlsx";
        return ResponseEntity.ok()
            .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
            .body(resource);
    }

    @GetMapping("/retry")
    public ResponseEntity<Resource> reDownloadCustomerTemplates() {
        Resource resource = templateService.downloadCustomerTemplates(true);
        String fileName = "retry_customer_templates.xlsx";
        return ResponseEntity.ok()
            .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
            .body(resource);
    }

    /**
     * Retrieves all template configurations
     *
     * @return List of all template configurations
     */
    @GetMapping("/configs")
    public ResponseEntity<List<TemplateConfig>> getAllTemplateConfigs() {
        log.info("Retrieving all template configurations");
        return ResponseEntity.ok(templateConfigService.findAll());
    }

    /**
     * Retrieves a specific template configuration by key
     *
     * @param key The unique key of the template configuration
     * @return The template configuration if found
     */
    @GetMapping("/configs/{key}")
    public ResponseEntity<TemplateConfig> getTemplateConfig(@PathVariable String key) {
        log.info("Retrieving template configuration with key: {}", key);
        return ResponseEntity.ok(templateConfigService.findByKey(key));
    }

    /**
     * Creates a new template configuration
     *
     * @param templateConfig The template configuration to create
     * @return The created template configuration
     */
    @PostMapping("/configs")
    public ResponseEntity<TemplateConfig> createTemplateConfig(@RequestBody TemplateConfig templateConfig) {
        log.info("Creating new template configuration with key: {}", templateConfig.getKey());
        return ResponseEntity.ok(templateConfigService.create(templateConfig));
    }

    /**
     * Updates an existing template configuration
     *
     * @param key            The key of the template configuration to update
     * @param templateConfig The updated template configuration
     * @return The updated template configuration
     */
    @PutMapping("/configs/{key}")
    public ResponseEntity<TemplateConfig> updateTemplateConfig(
        @PathVariable String key,
        @RequestBody TemplateConfig templateConfig) {
        log.info("Updating template configuration with key: {}", key);
        return ResponseEntity.ok(templateConfigService.update(key, templateConfig));
    }

    /**
     * Deletes a template configuration
     *
     * @param key The key of the template configuration to delete
     * @return No content response
     */
    @DeleteMapping("/configs/{key}")
    public ResponseEntity<Void> deleteTemplateConfig(@PathVariable String key) {
        log.info("Deleting template configuration with key: {}", key);
        templateConfigService.delete(key);
        return ResponseEntity.noContent().build();
    }
}
