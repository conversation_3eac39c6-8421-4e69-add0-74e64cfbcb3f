package com.asw.middleware.model.middleware;

import com.asw.middleware.enums.OrderStatus;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "\"order\"", schema = "dbo")
public class Order {

    @Id
    @Column(name = "id", nullable = false, length = 50)
    private String id;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.MERGE, CascadeType.PERSIST})
    @JoinColumn(name = "bu_code")
    private Header header;

    @Column(name = "creation_time")
    private Instant creationTime;

    @Column(name = "cons_code", length = 50)
    private String consCode;

    @Column(name = "warehouse", length = 100)
    private String warehouse;

    @Column(name = "update_time")
    private Instant updateTime;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.MERGE, CascadeType.PERSIST})
    @JoinColumn(name = "user_code")
    private User user;

    @Column(name = "total_price", precision = 10, scale = 2)
    private BigDecimal totalPrice;

    @Column(name = "total_discounts", precision = 10, scale = 2)
    private BigDecimal totalDiscounts;

    @Column(name = "total_tax", precision = 10, scale = 2)
    private BigDecimal totalTax;

    @Column(name = "delivery_cost", precision = 10, scale = 2)
    private BigDecimal deliveryCost;

    @Column(name = "currency", length = 50)
    private String currency;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 50)
    private OrderStatus status;

    @Column(name = "consignment_status", length = 50)
    private String consignmentStatus;

    @Column(name = "payment_status", length = 50)
    private String paymentStatus;

    @Column(name = "delivery_mode", length = 50)
    private String deliveryMode;

    @Column(name = "email")
    private String email;

    @OneToOne(fetch = FetchType.LAZY, cascade = {CascadeType.MERGE, CascadeType.PERSIST})
    @JoinColumn(name = "shipping_info_id")
    private ShippingInfo shippingInfo;

    @OneToOne(fetch = FetchType.LAZY, cascade = {CascadeType.MERGE, CascadeType.PERSIST})
    @JoinColumn(name = "billing_info_id")
    private BillingInfo billingInfo;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.MERGE, CascadeType.PERSIST})
    @JoinColumn(name = "payments_id")
    private Payment payment;

    @Column(name = "reference_ex_rate", precision = 10, scale = 5)
    private BigDecimal referenceExRate;

    @Column(name = "is_milk_powder")
    private Boolean isMilkPowder;

    @Column(name = "post_code", length = 50)
    private String postCode;

    @Column(name = "ecno", length = 50)
    private String ecno;

    @Column(name = "odno", length = 50)
    private String odno;

    @Column(name = "amt", precision = 10, scale = 2)
    private BigDecimal amt;

    @Column(name = "prodmn", length = 100)
    private String prodmn;

    @Column(name = "realmt", length = 100)
    private String realmt;

    @Column(name = "trade_type", length = 50)
    private String tradeType;

    @Column(name = "ser_code", length = 50)
    private String serCode;

    @Column(name = "ecndo", length = 50)
    private String ecndo;

    @Column(name = "service_charge", precision = 10, scale = 2)
    private BigDecimal serviceCharge;

    @Column(name = "dead_line")
    private Instant deadLine;

    @Column(name = "original_order_id", length = 50)
    private String originalOrderId;

    @Column(name = "original_creation_time")
    private Instant originalCreationTime;

    @Column(name = "special_instruction")
    private String specialInstruction;

    @Column(name = "delivery_recycle_collect_request")
    private Boolean deliveryRecycleCollectRequest;

    @Column(name = "pack_type", length = 50)
    private String packType;

    @Column(name = "delivery_date")
    private LocalDate deliveryDate;

    @Column(name = "delivery_time_slot", length = 50)
    private String deliveryTimeSlot;

    @Column(name = "show_price")
    private Boolean showPrice;

    @Column(name = "invoice_file_name")
    private String invoiceFileName;

    @Column(name = "pick_type", length = 50)
    private String pickType;

    @Column(name = "shipping_location")
    private String shippingLocation;

    @Column(name = "cold_bag_needed")
    private Boolean coldBagNeeded;

    @Column(name = "parent_order_id", length = 50)
    private String parentOrderId;

    @Column(name = "child_order_id", length = 50)
    private String childOrderId;

    @Column(name = "process_status")
    private String processStatus;

    @OneToMany(mappedBy = "order", cascade = {CascadeType.MERGE, CascadeType.PERSIST})
    private List<Consignment> consignments;

    @Column(name = "created_on")
    private Instant createdOn;

    @Column(name = "created_by_xml", length = 50)
    private String createdByXml;

    @Column(name = "updated_on")
    private Instant updatedOn;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.MERGE, CascadeType.PERSIST})
    @JoinColumn(name = "user_template_id")
    private CustomerTemplate customerTemplate;

    @Column(name = "is_created_in_som")
    private Boolean isCreatedInSOM = false;

    @Column(name = "memo", columnDefinition = "NVARCHAR(MAX) COLLATE Chinese_PRC_CI_AS")
    private String memo;

}