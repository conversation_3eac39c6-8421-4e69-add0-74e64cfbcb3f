package com.asw.middleware.model.middleware;

import com.asw.middleware.enums.CustomerTemplateStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.OffsetDateTime;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Builder
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "customer_template")
public class CustomerTemplate {

    @Id
    @Column(name = "id", length = 36)
    private String id;

    @Column(name = "sc_user_code", length = 50)
    private String scUserCode;

    @Column(name = "sc_account_no", length = 50)
    private String scAccountNo;

    @Column(name = "som_customer_id", length = 50)
    private String somCustomerId;

    @Column(name = "som_account_no", length = 50)
    private String somAccountNo;

    @Column(name = "modified_on", length = 50)
    private String modifiedOn;

    @Column(name = "company_code")
    private String companyCode;

    @Column(name = "name", columnDefinition = "NVARCHAR(MAX) COLLATE Chinese_PRC_CI_AS")
    private String name;

    @Column(name = "long_name", columnDefinition = "NVARCHAR(MAX) COLLATE Chinese_PRC_CI_AS")
    private String longName;

    @Column(name = "first_name", columnDefinition = "NVARCHAR(MAX) COLLATE Chinese_PRC_CI_AS")
    private String firstName;

    @Column(name = "trade_class")
    private String tradeClass;

    @Column(name = "marketing_group_id")
    private String marketingGroupId;

    @Column(name = "sub_marketing_group_id")
    private String subMarketingGroupId;

    @Column(name = "room", columnDefinition = "NVARCHAR(100) COLLATE Chinese_PRC_CI_AS")
    private String room;

    @Column(name = "floor", columnDefinition = "NVARCHAR(100) COLLATE Chinese_PRC_CI_AS")
    private String floor;

    @Column(name = "block", columnDefinition = "NVARCHAR(100) COLLATE Chinese_PRC_CI_AS")
    private String block;

    @Column(name = "location", columnDefinition = "NVARCHAR(MAX) COLLATE Chinese_PRC_CI_AS")
    private String location;

    @Column(name = "building", columnDefinition = "NVARCHAR(MAX) COLLATE Chinese_PRC_CI_AS")
    private String building;

    @Column(name = "building_id")
    private String buildingId;

    @Column(name = "street_no", columnDefinition = "NVARCHAR(100) COLLATE Chinese_PRC_CI_AS")
    private String streetNo;

    @Column(name = "street", columnDefinition = "NVARCHAR(MAX) COLLATE Chinese_PRC_CI_AS")
    private String street;

    @Column(name = "street_id")
    private String streetId;

    @Column(name = "district", columnDefinition = "NVARCHAR(MAX) COLLATE Chinese_PRC_CI_AS")
    private String district;

    @Column(name = "district_id")
    private String districtId;

    @Column(name = "area", columnDefinition = "NVARCHAR(MAX) COLLATE Chinese_PRC_CI_AS")
    private String area;

    @Column(name = "area_id")
    private String areaId;

    @Column(name = "email")
    private String email;

    @Column(name = "mobile")
    private String mobile;

    @Column(name = "phone")
    private String phone;

    @Column(name = "whatsapp")
    private String whatsApp;

    @Column(name = "created_on")
    @CreationTimestamp
    private OffsetDateTime createdOn;

    @Column(name = "updated_on")
    @UpdateTimestamp
    private OffsetDateTime updatedOn;

    @Enumerated(EnumType.STRING)
    private CustomerTemplateStatus status = CustomerTemplateStatus.PENDING;

    @Column(name = "last_retry_time")
    private OffsetDateTime lastRetryTime;

    @Column(name = "first_retry_time")
    private OffsetDateTime firstRetryTime;

    @Column(name = "is_address_changed")
    private Boolean isAddressChanged;

    @Column(name = "is_primary_address")
    private Boolean isPrimaryAddress;

    /**
     * Checks if there are changes in address-related fields between this template and another template
     *
     * @param other The other CustomerTemplate to compare with
     * @return true if there are changes in address-related fields, false otherwise
     */
    public boolean hasChangeAddress(CustomerTemplate other) {
        return !Objects.equals(this.getFirstName(), other.getFirstName()) ||
            !Objects.equals(this.getName(), other.getName()) ||
            !Objects.equals(this.getDistrict(), other.getDistrict()) ||
            !Objects.equals(this.getPhone(), other.getPhone()) ||
            !Objects.equals(this.getRoom(), other.getRoom()) ||
            !Objects.equals(this.getFloor(), other.getFloor()) ||
            !Objects.equals(this.getBlock(), other.getBlock()) ||
            !Objects.equals(this.getBuilding(), other.getBuilding()) ||
            !Objects.equals(this.getBuildingId(), other.getBuildingId()) ||
            !Objects.equals(this.getStreetNo(), other.getStreetNo()) ||
            !Objects.equals(this.getStreet(), other.getStreet()) ||
            !Objects.equals(this.getStreetId(), other.getStreetId()) ||
            !Objects.equals(this.getDistrictId(), other.getDistrictId()) ||
            !Objects.equals(this.getArea(), other.getArea()) ||
            !Objects.equals(this.getAreaId(), other.getAreaId());
    }
}
