package com.asw.middleware.service;

import com.asw.middleware.enums.TemplateConfigType;
import com.asw.middleware.model.middleware.TemplateConfig;
import com.asw.middleware.model.middleware.CustomerTemplate;
import com.asw.middleware.repository.middleware.TemplateConfigRepository;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service class for managing template configurations. Provides CRUD operations and specialized queries for template configurations.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateConfigService {

    private static final List<TemplateConfigType> DEFAULT_FIELDS = Arrays.asList(
        TemplateConfigType.COMPANY_CODE, TemplateConfigType.MARKETING_GROUP_ID, TemplateConfigType.SUB_MARKETING_GROUP_ID
    );

    private final TemplateConfigRepository templateConfigRepository;

    /**
     * Initializes default template configurations in the database. This method runs after dependency injection is complete. If configurations already
     * exist, initialization is skipped.
     */
    @PostConstruct
    @Transactional
    public void initializeDefaultValues() {
        if (templateConfigRepository.count() > 0) {
            log.info("Default values already exist in database");
            return;
        }

        List<TemplateConfig> defaultValues = Arrays.stream(TemplateConfigType.values())
            .map(type -> TemplateConfig.builder()
                .key(type.name().toLowerCase())
                .type(type.getType())
                .defaultValue(type.getDefaultValue())
                .description(type.getDescription())
                .build())
            .toList();

        templateConfigRepository.saveAll(defaultValues);
        log.info("Initialized default values in database");
    }

    /**
     * Retrieves template configurations by their types.
     *
     * @param types List of template configuration types to filter by
     * @return List of matching template configurations. Returns empty list if types is null or empty
     */
    public List<TemplateConfig> findByTypes(List<TemplateConfigType> types) {
        if (types == null || types.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> typeNames = types.stream()
            .map(TemplateConfigType::getType)
            .collect(Collectors.toList());

        return templateConfigRepository.findByTypeIn(typeNames);
    }

    /**
     * Retrieves all template configurations from the database.
     *
     * @return List of all template configurations
     */
    public List<TemplateConfig> findAll() {
        return templateConfigRepository.findAll();
    }

    /**
     * Retrieves a specific template configuration by its key.
     *
     * @param key The unique identifier of the template configuration
     * @return The template configuration if found
     * @throws NoSuchElementException if no configuration exists with the given key
     */
    public TemplateConfig findByKey(String key) {
        return templateConfigRepository.findById(key)
            .orElseThrow(() -> new NoSuchElementException("Template configuration not found with key: " + key));
    }

    /**
     * Creates a new template configuration. The key must be unique among all existing configurations.
     *
     * @param templateConfig The template configuration to create. Must contain a unique key
     * @return The created template configuration with any auto-generated fields populated
     * @throws IllegalStateException if a configuration already exists with the same key
     */
    @Transactional
    public TemplateConfig create(TemplateConfig templateConfig) {
        if (templateConfigRepository.existsById(templateConfig.getKey())) {
            throw new IllegalStateException("Template configuration already exists with key: " + templateConfig.getKey());
        }
        return templateConfigRepository.save(templateConfig);
    }

    /**
     * Updates an existing template configuration. The key cannot be changed during update.
     *
     * @param key            The key of the template configuration to update
     * @param templateConfig The new configuration values. The key field will be ignored
     * @return The updated template configuration
     * @throws NoSuchElementException if no configuration exists with the given key
     */
    @Transactional
    public TemplateConfig update(String key, TemplateConfig templateConfig) {
        TemplateConfig existing = findByKey(key);

        existing.setType(templateConfig.getType());
        existing.setDefaultValue(templateConfig.getDefaultValue());
        existing.setDescription(templateConfig.getDescription());

        return templateConfigRepository.save(existing);
    }

    /**
     * Deletes a template configuration.
     *
     * @param key The key of the template configuration to delete
     * @throws NoSuchElementException if no configuration exists with the given key
     */
    @Transactional
    public void delete(String key) {
        if (!templateConfigRepository.existsById(key)) {
            throw new NoSuchElementException("Template configuration not found with key: " + key);
        }
        templateConfigRepository.deleteById(key);
    }

    /**
     * Applies default template configuration values to a list of CustomerTemplate if values are missing
     *
     * @param customerTemplates List of CustomerTemplate objects
     */
    public void applyDefaultTemplateConfig(List<CustomerTemplate> customerTemplates) {
        List<TemplateConfig> configs = findByTypes(DEFAULT_FIELDS);
        customerTemplates.forEach(customerTemplate -> applyDefaultTemplateConfig(configs, customerTemplate));
    }

    public void applyDefaultTemplateConfig(CustomerTemplate customerTemplate) {
        List<TemplateConfig> configs = findByTypes(DEFAULT_FIELDS);
        applyDefaultTemplateConfig(configs, customerTemplate);
    }

    private void applyDefaultTemplateConfig(List<TemplateConfig> configs, CustomerTemplate customerTemplate) {
        configs.forEach(config -> {
            switch (TemplateConfigType.fromType(config.getType())) {
                case COMPANY_CODE -> {
                    if (StringUtils.isBlank(customerTemplate.getCompanyCode())) {
                        customerTemplate.setCompanyCode(config.getDefaultValue());
                    }
                }
                case MARKETING_GROUP_ID -> {
                    if (StringUtils.isBlank(customerTemplate.getMarketingGroupId())) {
                        customerTemplate.setMarketingGroupId(config.getDefaultValue());
                    }
                }
                case SUB_MARKETING_GROUP_ID -> {
                    if (StringUtils.isBlank(customerTemplate.getSubMarketingGroupId())) {
                        customerTemplate.setSubMarketingGroupId(config.getDefaultValue());
                    }
                }
            }
        });
    }

    public Integer getRetryInterval() {
        TemplateConfig config = findByKey(TemplateConfigType.RETRY_INTERVAL.name().toLowerCase());
        return config != null ? Integer.parseInt(config.getDefaultValue()) : 30; // Default 30 minutes
    }

    public Integer getRetryMaxHours() {
        TemplateConfig config = findByKey(TemplateConfigType.RETRY_AVAILABLE_AFTER_HOURS.name().toLowerCase());
        return config != null ? Integer.parseInt(config.getDefaultValue()) : 48; // Default 48 hours
    }
}