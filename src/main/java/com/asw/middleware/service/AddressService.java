package com.asw.middleware.service;

import com.asw.middleware.client.SOMClient;
import com.asw.middleware.dto.SOMBuilding;
import com.asw.middleware.dto.SOMStreet;
import com.asw.middleware.mapper.AddressMapper;
import com.asw.middleware.mapper.BuildingMapper;
import com.asw.middleware.model.middleware.Address;
import com.asw.middleware.model.middleware.Building;
import com.asw.middleware.repository.middleware.AddressRepository;
import com.asw.middleware.repository.middleware.BuildingRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class AddressService {

    private final AddressRepository addressRepository;
    private final BuildingRepository buildingRepository;
    private final SOMClient somClient;
    private final BuildingMapper buildingMapper;
    private final AddressMapper addressMapper;

    @Getter
    public enum AddressType {
        AREA("area"),
        DISTRICT("district"),
        STREET("street"),
        STREET_NO("street_no"),
        BUILDING("building"),
        FLOOR("floor"),
        ROOM("room");

        private final String value;

        AddressType(String value) {
            this.value = value;
        }
    }

    public Map<AddressType, Pair<String, String>> breakAddressLine(String addressLine1, String addressLine2) {

        // detect chinses or english by find chinses char
        if (addressLine1.matches(".*[\\u4e00-\\u9fa5].*")) {
            // Chinese address
            return breakChineseAddress(addressLine1, addressLine2);
        } else {
            // English address
            return breakEnglishAddress(addressLine1, addressLine2);
        }
    }

    private Map<AddressType, Pair<String, String>> breakAddress(String addressLine1, String addressLine2,
        boolean isChinese) {
        Map<AddressType, Pair<String, String>> result = new HashMap<>();
        addressLine1.replace(",", "");
        Optional<String> buildingOpt = findBuilding(addressLine1, isChinese);

        if (buildingOpt.isPresent()) {
            Building building = buildingRepository.findById(buildingOpt.get()).get();
            result.put(AddressType.BUILDING,
                Pair.of(building.getExternalId(), isChinese ? building.getChineseName() : building.getName()));
            result.put(AddressType.STREET_NO,
                Pair.of(null, building.getStreetNo() != null ? building.getStreetNo() : ""));
            addressLine1 = addressLine1.replace(isChinese ? building.getChineseName() : building.getName(), "");

            List<Address> addressList = addressRepository.findAllById(
                List.of(building.getStreetId(), building.getDistrictId(), building.getAreaId())
            );

            for (Address address : addressList) {
                AddressType addressType = AddressType.valueOf(address.getType().toUpperCase());
                result.put(addressType,
                    Pair.of(address.getExternalId(), isChinese ? address.getChineseName() : address.getName()));
                addressLine1 = addressLine1.replace(isChinese ? address.getChineseName() : address.getName(), "");
            }
        } else {
            Optional<String> streetOpt = findStreet(addressLine1, isChinese);
            if (streetOpt.isPresent()) {
                Address street = addressRepository.findById(streetOpt.get()).get();
                result.put(AddressType.STREET,
                    Pair.of(street.getExternalId(), isChinese ? street.getChineseName() : street.getName()));
                addressLine1 = addressLine1.replace(isChinese ? street.getChineseName() : street.getName(), "");

                List<Address> addresses = addressRepository.findAllById(
                    Stream.of(street.getStreetId(), street.getDistrictId(), street.getAreaId()).filter(
                        Objects::nonNull).toList());

                for (var addr : addresses) {
                    AddressType addressType = AddressType.valueOf(addr.getType().toUpperCase());
                    result.put(addressType,
                        Pair.of(addr.getExternalId(), isChinese ? addr.getChineseName() : addr.getName()));
                    addressLine1 = addressLine1.replace(isChinese ? addr.getChineseName() : addr.getName(), "");
                }

//                var streetBuilding = addressLine1.split(isChinese ? " " : ",");
//                if (streetBuilding.length > 1) {
//                    if (isChinese) {
//                        result.put(AddressType.BUILDING, Pair.of(null, streetBuilding[0]));
//                        result.put(AddressType.STREET_NO, Pair.of(null, streetBuilding[1]));
//                    } else {
//                        result.put(AddressType.STREET_NO, Pair.of(null, streetBuilding[0]));
//                        result.put(AddressType.BUILDING, Pair.of(null, streetBuilding[1]));
//                    }
//                } else {
//                    result.put(AddressType.BUILDING, Pair.of(null, addressLine1));
//                }
            } else {
//                result.putIfAbsent(AddressType.STREET, Pair.of(null, addressLine1));
            }
        }

        parseFloorAndUnit(addressLine2, result);
        return result;
    }

    private Optional<String> findBuilding(String addressLine1, boolean isChinese) {
        Map<String, String> buildingMap = isChinese ? mapChineseBuildingNameToBuildingId : mapBuildingNameToBuildingId;
        for (int i = 0; i < addressLine1.length(); i++) {
            for (int j = addressLine1.length(); j > i; j--) {
                String tempBuilding = addressLine1.substring(i, j);
                if (buildingMap.containsKey(tempBuilding.toLowerCase())) {
                    return Optional.of(buildingMap.get(tempBuilding.toLowerCase()));
                }
            }
        }
        return Optional.empty();
    }

    private Optional<String> findStreet(String addressLine1, boolean isChinese) {
        Map<String, String> streetMap = isChinese ? mapChineseStreetNameToStreetId : mapStreetNameToStreetId;
        for (int i = 0; i < addressLine1.length(); i++) {
            for (int j = addressLine1.length(); j > i; j--) {
                String tempStreet = addressLine1.substring(i, j);
                if (streetMap.containsKey(tempStreet.toLowerCase())) {
                    return Optional.of(streetMap.get(tempStreet.toLowerCase()));
                }
            }
        }
        return Optional.empty();
    }

    private void parseFloorAndUnit(String addressLine2, Map<AddressType, Pair<String, String>> result) {
        var floorUnit = addressLine2.split(",");
        result.put(AddressType.ROOM, Pair.of(null, floorUnit[0].trim()));
        if (floorUnit.length > 1) {
            result.put(AddressType.FLOOR, Pair.of(null, floorUnit[1].trim()));
        }
    }

    private Map<AddressType, Pair<String, String>> breakChineseAddress(String addressLine1, String addressLine2) {
        return breakAddress(addressLine1, addressLine2, true);
    }

    private Map<AddressType, Pair<String, String>> breakEnglishAddress(String addressLine1, String addressLine2) {
        return breakAddress(addressLine1, addressLine2, false);
    }

    Map<String, String> mapBuildingNameToBuildingId = new HashMap<>();
    Map<String, String> mapStreetNameToStreetId = new HashMap<>();
    Map<String, String> mapDistrictNameToDistrictId = new HashMap<>();
    Map<String, String> mapAreaNameToAreaId = new HashMap<>();

    Map<String, String> mapChineseBuildingNameToBuildingId = new HashMap<>();
    Map<String, String> mapChineseStreetNameToStreetId = new HashMap<>();
    Map<String, String> mapChineseDistrictNameToDistrictId = new HashMap<>();
    Map<String, String> mapChineseAreaNameToAreaId = new HashMap<>();

    @EventListener
    public void onStartup(ApplicationReadyEvent event) {
        try {
            List<Address> areas = Address.parseAddressFromJson("src/main/resources/api/areas.json", "area");
            addressRepository.saveAll(areas);
            log.info("Parsed {} areas", areas.size());
            areas.forEach(area -> {
                mapAreaNameToAreaId.put(area.getName().toLowerCase(), area.getExternalId());
                mapChineseAreaNameToAreaId.put(area.getChineseName().toLowerCase(), area.getExternalId());
            });

            List<Address> districts = Address.parseAddressFromJson("src/main/resources/api/districts.json", "district");
            addressRepository.saveAll(districts);
            log.info("Parsed {} districts", districts.size());
            districts.forEach(district -> {
                mapDistrictNameToDistrictId.put(district.getName().toLowerCase(), district.getExternalId());
                mapChineseDistrictNameToDistrictId.put(district.getChineseName().toLowerCase(), district.getExternalId());
            });

            List<SOMStreet> somStreets = somClient.fetchAllStreets();
            List<Address> streets = addressMapper.toAddressEntityList(somStreets);
            addressRepository.saveAllNewEntities(streets);
            log.info("Parsed {} streets", streets.size());
//        List<Address> streets = addressRepository.findAllByType(AddressType.STREET.getValue());
            streets.forEach(street -> {
                mapStreetNameToStreetId.put(street.getName().toLowerCase(), street.getExternalId());
                mapChineseStreetNameToStreetId.put(street.getChineseName().toLowerCase(), street.getExternalId());
            });

            List<SOMBuilding> somBuildings = somClient.fetchAllBuildings();
            List<Building> buildings = buildingMapper.toBuildingEntityList(somBuildings);
            buildingRepository.saveAllNewEntities(buildings);
            log.info("Parsed {} buildings", buildings.size());
//        List<Building> buildings = buildingRepository.findAll();
            buildings.forEach(building -> {
                mapBuildingNameToBuildingId.put(building.getName().toLowerCase(), building.getExternalId());
                mapChineseBuildingNameToBuildingId.put(building.getChineseName().toLowerCase(), building.getExternalId());
            });

            log.debug("break CHINESE {}", breakAddressLine("美樂大廈4號 香港仔中心海珍閣(A座)", "Unit: G, Floor: 10"));
            log.debug("break ENGLISH {}",
                    breakAddressLine("FO FRESH FISH TRADERS' SCHOOL,  NO. 33 SYCAMORE STREET", "Unit: 11, Floor: 111"));
            log.info("Finished loading address data");
        } catch (Throwable throwable) {
            log.warn(throwable.getMessage());
        }
    }
}
