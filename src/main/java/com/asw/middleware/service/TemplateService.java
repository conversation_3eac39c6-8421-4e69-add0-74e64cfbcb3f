package com.asw.middleware.service;

import com.asw.middleware.client.SOMClient;
import com.asw.middleware.dto.SOMAccount;
import com.asw.middleware.dto.SOMCustomer;
import com.asw.middleware.enums.CustomerTemplateStatus;
import com.asw.middleware.mapper.CustomerMapper;
import com.asw.middleware.model.middleware.CustomerTemplate;
import com.asw.middleware.model.middleware.Order;
import com.asw.middleware.model.middleware.ShippingInfo;
import com.asw.middleware.repository.middleware.CustomerTemplateRepository;
import com.asw.middleware.repository.middleware.OrderRepository;
import com.asw.middleware.service.AddressService.AddressType;
import com.asw.middleware.utils.JsonUtils;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.IntStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service class for handling template operations. Provides functionality to generate and manage Excel templates for user data.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class TemplateService {

    private final OrderRepository orderRepository;

    private final CustomerTemplateRepository customerTemplateRepository;
    private final TemplateConfigService templateConfigService;
    private final CustomerMapper customerMapper;
    private final AddressService addressService;
    private final SOMClient somClient;
    private final ApplicationContext applicationContext;

    public Resource downloadCustomerTemplates(boolean isRetry) {

        List<CustomerTemplate> customerTemplates;
        if (isRetry) {
            log.info("Getting templates with AVAILABLE_FOR_RETRY status for retry");
            customerTemplates = customerTemplateRepository.findAllByStatusInAndIsPrimaryAddressTrue(
                List.of(CustomerTemplateStatus.AVAILABLE_FOR_RETRY));
        } else {
            log.info("Getting templates with PENDING or DOWNLOADED status");
            customerTemplates = customerTemplateRepository.findAllByStatusInAndIsPrimaryAddressTrue(
                List.of(CustomerTemplateStatus.PENDING, CustomerTemplateStatus.DOWNLOADED, CustomerTemplateStatus.AVAILABLE_FOR_RETRY)
            );
        }

        templateConfigService.applyDefaultTemplateConfig(customerTemplates);

        customerTemplates.stream()
            .filter(template -> template.getStatus() != CustomerTemplateStatus.PENDING)
            .forEach(this::isRetrievedSOMCustomerData);

        List<CustomerTemplate> templatesForExcel = customerTemplates.stream()
            .filter(template -> template.getStatus() != CustomerTemplateStatus.UPLOADED)
            .toList();

        try {
            log.info("Generating Excel file for {} templates", templatesForExcel.size());
            byte[] excelBytes = generateTemplateExcel(templatesForExcel);

            customerTemplates.forEach(template -> {
                if (template.getStatus() == CustomerTemplateStatus.PENDING) {
                    log.info("Marking template {} as DOWNLOADED", template.getScUserCode());
                    template.setStatus(CustomerTemplateStatus.DOWNLOADED);
                }
            });

            customerTemplateRepository.saveAll(customerTemplates);

            return new ByteArrayResource(excelBytes);
        } catch (IOException e) {
            log.error("Error generating templates: {}", e.getMessage(), e);
            throw new RuntimeException("Error generating templates", e);
        }
    }

    /**
     * Generates an Excel file containing user template data by copying from template file
     *
     * @param customerTemplates list of user templates to include in the Excel file
     * @return byte array containing the Excel file data
     * @throws IOException if there is an error generating the Excel file
     */
    public byte[] generateTemplateExcel(List<CustomerTemplate> customerTemplates) throws IOException {
        try (InputStream templateStream = getClass().getResourceAsStream("/template/customer_template.xlsx")) {
            assert templateStream != null;
            try (XSSFWorkbook workbook = new XSSFWorkbook(templateStream);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

                // Get the first sheet (Customer Template)
                Sheet sheet = workbook.getSheetAt(0);

                // Get styles from template before clearing data
                Row headerRow = sheet.getRow(0);
                CellStyle oddRowStyle = sheet.getRow(1) != null ? sheet.getRow(1).getCell(0).getCellStyle() : null;
                CellStyle evenRowStyle = sheet.getRow(2) != null ? sheet.getRow(2).getCell(0).getCellStyle() : null;

                // Clear existing data rows (keep header row)
                int lastRowNum = sheet.getLastRowNum();
                for (int i = 1; i <= lastRowNum; i++) {
                    Row row = sheet.getRow(i);
                    if (row != null) {
                        sheet.removeRow(row);
                    }
                }

                int rowNum = 1;
                for (CustomerTemplate ut : customerTemplates) {
                    Row row = sheet.createRow(rowNum++);
                    fillCustomerTemplateRow(row, ut, rowNum % 2 == 0 ? evenRowStyle : oddRowStyle);
                }

                // Auto-size columns
                for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                    sheet.autoSizeColumn(i);
                }

                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        }
    }

    private void fillCustomerTemplateRow(Row row, CustomerTemplate customerTemplate, CellStyle style) {
        Row headerRow = row.getSheet().getRow(0);
        Map<String, Integer> columnIndexMap = new HashMap<>();

        // Create map of column names to their indices
        IntStream.range(0, headerRow.getLastCellNum())
            .mapToObj(headerRow::getCell)
            .filter(Objects::nonNull)
            .forEach(cell -> columnIndexMap.put(cell.getStringCellValue(), cell.getColumnIndex()));

        // Fill data based on column names
        fillCellIfColumnExists(row, columnIndexMap, "Company Code", customerTemplate.getCompanyCode(), style);
        fillCellIfColumnExists(row, columnIndexMap, "Name", customerTemplate.getName(), style);
        fillCellIfColumnExists(row, columnIndexMap, "Last Name", customerTemplate.getLongName(), style);
        fillCellIfColumnExists(row, columnIndexMap, "First Name", customerTemplate.getFirstName(), style);
        fillCellIfColumnExists(row, columnIndexMap, "Trade Class", customerTemplate.getTradeClass(), style);
        fillCellIfColumnExists(row, columnIndexMap, "MG", customerTemplate.getMarketingGroupId(), style);
        fillCellIfColumnExists(row, columnIndexMap, "SMG", customerTemplate.getSubMarketingGroupId(), style);
        fillCellIfColumnExists(row, columnIndexMap, "Room", customerTemplate.getRoom(), style);
        fillCellIfColumnExists(row, columnIndexMap, "Floor", customerTemplate.getFloor(), style);
        fillCellIfColumnExists(row, columnIndexMap, "Block", customerTemplate.getBlock(), style);
        fillCellIfColumnExists(row, columnIndexMap, "Location", customerTemplate.getLocation(), style);
        fillCellIfColumnExists(row, columnIndexMap, "Building", customerTemplate.getBuildingId(), style);
        fillCellIfColumnExists(row, columnIndexMap, "Street No", customerTemplate.getStreetNo(), style);
        fillCellIfColumnExists(row, columnIndexMap, "Street", customerTemplate.getStreetId(), style);
        fillCellIfColumnExists(row, columnIndexMap, "District", customerTemplate.getDistrictId(), style);
        fillCellIfColumnExists(row, columnIndexMap, "Area", customerTemplate.getAreaId(), style);
        fillCellIfColumnExists(row, columnIndexMap, "Email", customerTemplate.getEmail(), style);
        fillCellIfColumnExists(row, columnIndexMap, "Mobile", customerTemplate.getMobile(), style);
        fillCellIfColumnExists(row, columnIndexMap, "Phone", customerTemplate.getPhone(), style);
        fillCellIfColumnExists(row, columnIndexMap, "WhatsApp", customerTemplate.getWhatsApp(), style);
        fillCellIfColumnExists(row, columnIndexMap, "Approval Status", "Approved", style);
        fillCellIfColumnExists(row, columnIndexMap, "SC User Code", customerTemplate.getScUserCode(), style);
    }

    private void fillCellIfColumnExists(Row row, Map<String, Integer> columnIndexMap, String columnName, String value, CellStyle style) {
        Integer columnIndex = columnIndexMap.get(columnName);
        if (columnIndex != null) {
            setCellValue(row.createCell(columnIndex), value, style);
        } else {
            log.warn("Column '{}' not found in template", columnName);
        }
    }

    private void setCellValue(Cell cell, String value, CellStyle style) {
        cell.setCellValue(value != null ? value : "");
        cell.setCellStyle(style);
    }

    public void saveCustomerTemplate(Order order) {
        if (order.getUser() == null) {
            log.warn("User info is null in order {}", order.getId());
            return;
        }

        CustomerTemplate customerTemplate = customerMapper.orderToCustomerTemplate(order);
        parseAddressComponents(order.getShippingInfo(), customerTemplate);
        log.info("Received User Template: {}", JsonUtils.toJson(customerTemplate));

        var existingTemplate = customerTemplateRepository.findFirstByScUserCodeOrderByCreatedOnDesc(customerTemplate.getScUserCode());
        if (existingTemplate.isEmpty()) {
            log.info("Create new template for user {}", customerTemplate.getScUserCode());
            createCustomerTemplate(customerTemplate, order);
        } else {
            log.info("Handle existing template for user {}", customerTemplate.getScUserCode());
            handleExistingCustomerTemplate(customerTemplate, existingTemplate.get(), order);
        }
    }

    public void createCustomerTemplate(CustomerTemplate customerTemplate, Order order) {
        customerTemplate.setId(UUID.randomUUID().toString());
        templateConfigService.applyDefaultTemplateConfig(customerTemplate);
        customerTemplate.setTradeClass("1");
        customerTemplate.setStatus(CustomerTemplateStatus.PENDING);
        customerTemplate.setIsAddressChanged(false);
        customerTemplate.setIsPrimaryAddress(true);
        customerTemplate = customerTemplateRepository.save(customerTemplate);
        log.info("Created new template: {}", JsonUtils.toJson(customerTemplate));

        order.setCustomerTemplate(customerTemplate);
        orderRepository.save(order);
    }

    private void handleExistingCustomerTemplate(@NonNull CustomerTemplate newTemplate, @NonNull CustomerTemplate existingTemplate, Order order) {
        log.info("Existing template: {}", JsonUtils.toJson(existingTemplate));

        try {
            if (existingTemplate.hasChangeAddress(newTemplate)) {
                log.info("Shipping or address information has changed for template {}", existingTemplate.getScUserCode());

                existingTemplate.setIsAddressChanged(true);
                customerTemplateRepository.save(existingTemplate);

                // Create new template with SOM data from existing template
                newTemplate.setId(UUID.randomUUID().toString());
                newTemplate.setSomCustomerId(existingTemplate.getSomCustomerId());
                newTemplate.setSomAccountNo(existingTemplate.getSomAccountNo());
                newTemplate.setStatus(existingTemplate.getStatus());
                newTemplate.setIsAddressChanged(true);
                newTemplate.setIsPrimaryAddress(false);
                newTemplate.setTradeClass("1");
                newTemplate.setFirstRetryTime(existingTemplate.getFirstRetryTime());
                newTemplate.setLastRetryTime(existingTemplate.getLastRetryTime());
                templateConfigService.applyDefaultTemplateConfig(newTemplate);
                final var savedNewTemplate = customerTemplateRepository.save(newTemplate);

                order.setCustomerTemplate(savedNewTemplate);
                var updatedOrder = orderRepository.save(order);

                List<Order> referenceOrders = orderRepository.findByCustomerTemplateAndIsCreatedInSOMFalse(existingTemplate);
                if (referenceOrders.isEmpty()) {
                    log.info("Found 0 reference orders for template {}", existingTemplate.getScUserCode());
                    return;
                }
                referenceOrders.forEach(item -> item.setCustomerTemplate(savedNewTemplate));
                orderRepository.saveAll(referenceOrders);

                if (!StringUtils.isEmpty(savedNewTemplate.getSomCustomerId())) {
                    applicationContext.getBean(OrderService.class).createSOMOrder(updatedOrder);
                }

            } else {
                log.info("No changes in shipping or address information for template {}", existingTemplate.getScUserCode());
                order.setCustomerTemplate(existingTemplate);
                orderRepository.save(order);

                if (!StringUtils.isEmpty(existingTemplate.getSomCustomerId())) {
                    applicationContext.getBean(OrderService.class).createSOMOrder(order);
                }
            }
        } catch (Exception e) {
            log.error("Error handling existing template {}: {}", existingTemplate.getScUserCode(), e.getMessage(), e);
            throw e;
        }
    }

    private void parseAddressComponents(ShippingInfo shippingInfo, CustomerTemplate customerTemplate) {
        Map<AddressType, Pair<String /*id nullable*/ , String /*name nullable*/>> addressComponents = addressService.breakAddressLine(
            shippingInfo.getAddressLine() == null ? "" : shippingInfo.getAddressLine(),
            shippingInfo.getAddressLine2() == null ? "" : shippingInfo.getAddressLine2());
        log.debug("AddressComponents: {}", JsonUtils.toJson(addressComponents));

        Pair<String, String> roomPair = addressComponents.get(AddressType.ROOM);
        if (roomPair != null) {
            customerTemplate.setRoom(roomPair.getRight());
        }

        Pair<String, String> floorPair = addressComponents.get(AddressType.FLOOR);
        if (floorPair != null) {
            customerTemplate.setFloor(floorPair.getRight());
        }

        Pair<String, String> buildingPair = addressComponents.get(AddressType.BUILDING);
        if (buildingPair != null) {
            customerTemplate.setBuilding(buildingPair.getRight());
            customerTemplate.setBuildingId(buildingPair.getLeft());
        }

        Pair<String, String> streetNoPair = addressComponents.get(AddressType.STREET_NO);
        if (streetNoPair != null) {
            customerTemplate.setStreetNo(streetNoPair.getRight());
        }

        Pair<String, String> streetPair = addressComponents.get(AddressType.STREET);
        if (streetPair != null) {
            customerTemplate.setStreet(streetPair.getRight());
            customerTemplate.setStreetId(streetPair.getLeft());
        }

        Pair<String, String> districtPair = addressComponents.get(AddressType.DISTRICT);
        if (districtPair != null) {
            customerTemplate.setDistrict(districtPair.getRight());
            customerTemplate.setDistrictId(districtPair.getLeft());
        }

        Pair<String, String> areaPair = addressComponents.get(AddressType.AREA);
        if (areaPair != null) {
            customerTemplate.setArea(areaPair.getRight());
            customerTemplate.setAreaId(areaPair.getLeft());
        }
    }

    public boolean checkAndUpdateTemplateStatus(CustomerTemplate template) {
        // If template is in AVAILABLE_FOR_RETRY status, try to retrieve SOM data
        if (template.getStatus() == CustomerTemplateStatus.AVAILABLE_FOR_RETRY) {
            log.info("Checking AVAILABLE_FOR_RETRY template {}", template.getScUserCode());
            if (isRetrievedSOMCustomerData(template)) {
                log.info("Successfully retrieved SOM data for AVAILABLE_FOR_RETRY template {}", template.getScUserCode());
                return true;
            }
            return false;
        }

        // Check if it's time to retry
        if (template.getLastRetryTime() != null) {
            Integer retryInterval = templateConfigService.getRetryInterval();
            var nextRetryTime = template.getLastRetryTime().plusMinutes(retryInterval);
            if (nextRetryTime.isAfter(OffsetDateTime.now())) {
                log.info("Not yet time to retry for template {}, next retry time is {}", template.getScUserCode(), nextRetryTime);
                return false;
            }
        } else {
            template.setFirstRetryTime(OffsetDateTime.now());
        }

        // Check if maximum retry time has been exceeded
        Integer maxRetryHours = templateConfigService.getRetryMaxHours();
        if (template.getFirstRetryTime().plusHours(maxRetryHours).isBefore(OffsetDateTime.now())) {
            log.warn("Template {} has exceeded {} hours retry limit, marking as AVAILABLE_FOR_RETRY", template.getScUserCode(), maxRetryHours);
            template.setStatus(CustomerTemplateStatus.AVAILABLE_FOR_RETRY);
            return true;
        }

        // Try to retrieve SOM data
        if (isRetrievedSOMCustomerData(template)) {
            log.info("Successfully retrieved SOM data for template {}", template.getScUserCode());
            return true;
        }

        template.setLastRetryTime(OffsetDateTime.now());
        return true;
    }

    private boolean isRetrievedSOMCustomerData(CustomerTemplate template) {
        if (!StringUtils.isEmpty(template.getSomCustomerId()) && !StringUtils.isEmpty(template.getSomAccountNo())) {
            log.info("SOM Customer ID and Account No already exists for template {}, skipping retrieve", template.getScUserCode());
            return true;
        }

        SOMCustomer somCustomer = somClient.fetchFirstSOMCustomerByScUserCode(template.getScUserCode());
        if (somCustomer != null) {
            log.info("Fetched SOM Customer: {}", JsonUtils.toJson(somCustomer));
            template.setSomCustomerId(somCustomer.getId());
            template.setStatus(CustomerTemplateStatus.UPLOADED);
            template.setFirstRetryTime(null);
            template.setLastRetryTime(null);

            SOMAccount somAccount = somClient.fetchFirstSOMAccountBySOMCustomerId(somCustomer.getId());
            if (somAccount != null) {
                log.info("Found SOM account for customer {}: {}", somCustomer.getId(), JsonUtils.toJson(somAccount));
                template.setSomAccountNo(somAccount.getAccountNumber());
            } else {
                log.info("No SOM account found for customer {}", somCustomer.getId());
            }
            return true;
        }

        log.info("No SOM customer found for template {}", template.getScUserCode());
        return false;
    }
}