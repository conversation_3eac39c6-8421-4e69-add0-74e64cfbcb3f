package com.asw.middleware.service;

import static com.asw.middleware.utils.DateTimeFormatterUtils.formatDateTime;

import com.asw.middleware.client.FTPServiceClient;
import com.asw.middleware.client.SOMClient;
import com.asw.middleware.dto.*;
import com.asw.middleware.enums.OrderStatus;
import com.asw.middleware.model.middleware.*;
import com.asw.middleware.parser.OrderXMLParser;
import com.asw.middleware.parser.model.OrderCreationRequest;
import com.asw.middleware.repository.middleware.OrderCreationRequestLogRepository;
import com.asw.middleware.repository.middleware.OrderRepository;
import com.asw.middleware.repository.middleware.UserRepository;
import com.asw.middleware.utils.JsonUtils;
import com.asw.middleware.utils.PaymentChecker;
import java.io.File;
import java.net.URL;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderService {

    private final FTPServiceClient ftpServiceClient;
    private final OrderXMLParser orderXMLParser;
    private final OrderRepository orderRepository;
    private final UserRepository userRepository;
    private final OrderCreationRequestLogRepository orderCreationRequestLogRepository;
    private final SettingService settingService;
    private final ImportStatusService importStatusService;
    private final TemplateService templateService;
    private final SOMClient somClient;

    public final static String ORDER_CREATE_RETRY_LIMIT = "asw.settings.order.create.retry.limit";

    @Transactional(rollbackFor = Exception.class)
    public void retryFetchOrders() {
        log.debug("[OrderService] retry fetchOrders");
        processOrders(settingService.getSetting(FTPServiceClient.FTP_DIRECTORY_RETRY, "retry"));
    }

    @Transactional(rollbackFor = Exception.class)
    public void fetchOrders() {
        log.debug("[OrderService] fetchOrders");
        processOrders(settingService.getSetting(FTPServiceClient.FTP_DIRECTORY_CURRENT, "current"));
    }

    private void processOrders(String sourceFolder) {
        try {
            var remoteFilePaths = ftpServiceClient.listRemoteFiles(sourceFolder);
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            URL resource = classLoader.getResource("tmp");
            File tmpFolder = new File(resource.getPath());
            for (var remoteFilePath : remoteFilePaths) {
                log.info("Processing remote file path {}", remoteFilePath);
                try {
                    String fileName = remoteFilePath.replaceAll(sourceFolder + "/", "");
                    var localFilePath = tmpFolder.getAbsolutePath() + "/" + fileName;
                    var localFile = new File(localFilePath);
                    log.info("Retrieving {} to local file {}", remoteFilePath, localFile);
                    ftpServiceClient.downloadFile(remoteFilePath, localFilePath);
                    processOrderFile(localFile, remoteFilePath, sourceFolder);
                } catch (Exception exception) {
                    log.error("Processing error with remote order file {} - {}", remoteFilePath,
                        exception.getMessage(), exception);
                }
            }
        } catch (Exception exception) {
            log.error(exception.getMessage());
        }
    }

    private void processOrderFile(File localFile, String remoteFilePath, String sourceFolder) throws Exception {
        var orders = orderXMLParser.parse(localFile);
        var hasError = false;
        try {
            for (var order : orders) {
                String statusId = null;
                try {
                    statusId = importStatusService.startProcessOrder(order.getId(), remoteFilePath);

                    var optionalOrder = orderRepository.findById(order.getId());
                    if (optionalOrder.isEmpty()) {
                        handleNewOrder(order);
                    } else if (settingService.getSetting(FTPServiceClient.FTP_DIRECTORY_RETRY)
                        .equalsIgnoreCase(sourceFolder)) {

                        handleRetryOrder(order, optionalOrder.get());
                    } else {
                        log.info("Skipping existing order {}", order.getId());
                    }

                    importStatusService.processOrderSuccess(statusId);
                } catch (Exception e) {
                    hasError = true;
                    if (statusId == null) {
                        log.error("Failed to process order {} - {}", order.getId(), e.getMessage());
                        throw e;
                    }
                    importStatusService.processOrderFailed(statusId, e.getMessage());
                }
            }
        } finally {
            String destinationFolder = hasError
                ? settingService.getSetting(FTPServiceClient.FTP_DIRECTORY_ERROR)
                : settingService.getSetting(FTPServiceClient.FTP_DIRECTORY_ARCHIVED);

            String destinationFilePath = String.format("%s/%s", destinationFolder, getNewFileName(localFile));
            ftpServiceClient.renameRemoteFile(remoteFilePath, destinationFilePath);
        }
    }

    private void handleNewOrder(Order order) {
        var createdOrder = createOrder(order);
        templateService.saveCustomerTemplate(order);
        updateMiddlewareUser(createdOrder);
    }

    public void createSOMOrder(Order order) {
        if (Boolean.TRUE.equals(order.getIsCreatedInSOM())) {
            log.info("Order {} already created, skipping", order.getId());
            return;
        }

        boolean isInvalidPayment = PaymentChecker.isInvalidPayment(order);
        orderRepository.save(order);
        if  (isInvalidPayment) {
            return;
        }

        log.info("Creating SOM Order for id {}", order.getId());
        if (OrderStatus.CREATED.equals(order.getStatus())) {
            var somOrderType = somClient.fetchSOMOrderTypeByCode("I");
            var somCompany = somClient.fetchSOMCompanyByName("EM");
            var somAccount = somClient.fetchSOMAccountByNumber(order.getCustomerTemplate().getSomCustomerId());
            var somPaymentMethod = somClient.fetchSOMPaymentMethodByCode("GB");
            if (order.getConsignments().size() == 1) {
                // Single consignment
                log.info("Creating order {} - single consignments with cons code {}", order.getId(), order.getConsCode());
                retryCreateSOMOrder(
                        order,
                        order.getConsCode(),
                        order.getConsignments().get(0),
                        somOrderType,
                        somCompany,
                        somAccount,
                        somPaymentMethod
                );
            } else {
                // Multiple consignments - create defensive copy to avoid ConcurrentModificationException
                var consignmentsCopy = new ArrayList<>(order.getConsignments());
                for (var consignment : consignmentsCopy) {
                    log.info("Creating order {} - multiple consignments with cons code {}", order.getId(), consignment.getConsCode());
                    retryCreateSOMOrder(
                            order,
                            consignment.getConsCode(),
                            consignment,
                            somOrderType,
                            somCompany,
                            somAccount,
                            somPaymentMethod
                    );
                }
            }
        }
    }

    private void retryCreateSOMOrder(
        Order order,
        String consCode,
        Consignment consignment,
        SOMOrderType somOrderType,
        SOMCompany somCompany,
        SOMAccount somAccount,
        SOMPaymentMethod somPaymentMethod) {
        try {
            var retryLimit = Integer.parseInt(settingService.getSetting(ORDER_CREATE_RETRY_LIMIT, "99"));
            int retryCounter = 0;
            log.info("Retry to create SOM order with max attempt is {}", retryLimit);
            while (retryCounter <= retryLimit) {
                log.info("Creating order in SOM");
                var orderCreationRequest = buildOrderCreationRequest(
                    order,
                    consCode,
                    consignment,
                    somClient,
                    somOrderType,
                    somCompany,
                    somAccount,
                    somPaymentMethod
                );
                var orderCreationPayload = JsonUtils.objectMapper.writeValueAsString(orderCreationRequest);
                if (!createOrderInSOM(retryCounter, consCode, order, orderCreationPayload)) {
                    Thread.sleep(3000);
                    log.info("Retrying to create order {}", order.getId());
                    retryCounter++;
                } else {
                    break;
                }
            }
        } catch (Throwable throwable) {
            log.error(throwable.getMessage());
            throwable.printStackTrace();
        }
    }

    public static final long ORDER_LINE_TYPE_NORMAL = *********;

    public OrderCreationRequest buildOrderCreationRequest(
        Order order, String consCode, Consignment consignment,
        SOMClient localSomClient,
        SOMOrderType somOrderType, SOMCompany somCompany, SOMAccount somAccount,
        SOMPaymentMethod somPaymentMethod) {
        AtomicInteger sequenceCounter = new AtomicInteger();
        OrderCreationRequest.Address shippingAddress = null;
        // Final customer template is still not uploaded and it has address changes
        var customerTemplate = order.getCustomerTemplate();
        if (customerTemplate.getIsAddressChanged()) {
            // TODO: add primary address (the UserTemplate based on scUserCode and isPrimaryAddress TRUE)
            shippingAddress = OrderCreationRequest.Address.builder()
                .ttrRoom(customerTemplate.getRoom())
                .ttrFloor(customerTemplate.getFloor())
                .ttrSc(customerTemplate.getScUserCode())
                .ttrBlock(customerTemplate.getBlock())
                .ttrLocation(customerTemplate.getLocation())
                .ttrStreetno(customerTemplate.getStreetNo())
                .ttrBuilding(OrderCreationRequest.Address.Building.builder()
                    .ttrBuildingid(customerTemplate.getBuildingId())
                    .ttrName(customerTemplate.getBuilding())
                    .build())
                .ttrDistrict(OrderCreationRequest.Address.District.builder()
                    .ttrDistrictid(customerTemplate.getDistrictId())
                    .ttrCode(customerTemplate.getDistrict())
                    .build())
                .ttrStreet(OrderCreationRequest.Address.Street.builder()
                    .ttrStreetid(customerTemplate.getStreetId())
                    .ttrName(customerTemplate.getStreet())
                    .build())
                .ttrArea(OrderCreationRequest.Address.Area.builder()
                    .ttrAreaid(customerTemplate.getAreaId())
                    .ttrCode(customerTemplate.getArea())
                    .build())
                .build();
        }
        var company = OrderCreationRequest.Company.builder()
                .ttrCompanyid(Optional.ofNullable(somCompany).map(SOMCompany::getCompanyId).orElse(null))
                .ttrName(Optional.ofNullable(somCompany).map(SOMCompany::getName).orElse(null))
                .build();
        return OrderCreationRequest.builder()
                .ttrOrdertype(OrderCreationRequest.OrderType.builder()
                        .ttrOrdertypeid(Optional.ofNullable(somOrderType).map(SOMOrderType::getOrderTypeId).orElse(null))
                        .ttrCode(Optional.ofNullable(somOrderType).map(SOMOrderType::getCode).orElse(null))
                        .build())
                .ttrParentexternalordernumber(order.getId())
                .ttrCompany(company)
                .customerid(OrderCreationRequest.Account.builder()
                        .accountid(Optional.ofNullable(somAccount).map(SOMAccount::getCustomerId).orElse(null))
                        .accountnumber(Optional.ofNullable(somAccount).map(SOMAccount::getAccountNumber).orElse(null))
                        .ttrCompany(company)
                        .build())
                .ttrInvoicetemplate(OrderCreationRequest.COPSTeam.builder().build())
                .ttrBillto(null)
                .ttrFreeissue(false)
                .ttrPaymentmethod(OrderCreationRequest.PaymentMethod.builder()
                        .ttrPaymentmethodid(Optional.ofNullable(somPaymentMethod).map(SOMPaymentMethod::getPaymentMethodId).orElse(null))
                        .ttrKey(Optional.ofNullable(somPaymentMethod).map(SOMPaymentMethod::getCode).orElse(null))
                        .build())
                .ttrSalesman(null)
                .ttrDifferentaddress(shippingAddress != null)
                .ttrShippingaddress(shippingAddress)
                .ttrDifferentcontact(false)
                .ttrSecondcontactname(null)
                .ttrSecondcontactphone(null)
                .ttrRouting(OrderCreationRequest.Routing.builder()
                        .ttrDistrict(OrderCreationRequest.Routing.District.builder().build())
                        .ttrRoute(OrderCreationRequest.Routing.Route.builder().build())
                        .ttrStreet(OrderCreationRequest.Routing.Street.builder().build())
                        .ttrTruckcode(OrderCreationRequest.Truck.builder().build())
                        .build())
                .ttrDriver(null)
                .requestdeliveryby(null)
                .description(order.getSpecialInstruction())
                .transactioncurrencyid(OrderCreationRequest.Currency.builder()
                        .transactioncurrencyid(null)
                        .isocurrencycode("HKD")
                        .build())
                .ttrActualdate(null)
                .ttrActualdriver(null)
                .ttrActualtruck(null)
                .ttrInvoiceno(null)
                .ttrOldorderno(null)
                .ttrOriginalinvoiceno(null)
                .ttrPaymentstatus(*********)
                .ttrPonumber(consCode)
                .ttrRescheduledate(null)
                .ttrTimeofreschedule(null)
                .ttrRescheduledriver(null)
                .ttrRescheduletruck(null)
                .ttrReschedulestatus(null)
                .orderLines(consignment.getOrderEntries().stream().map(orderEntry -> {
                    var price = orderEntry.getBasePrice().doubleValue() - orderEntry.getDiscount().doubleValue();
                    SOMProduct somProduct = null;
                    SOMUoms somUoms = null;
                    if (localSomClient != null) {
                        somProduct = localSomClient.fetchSOMProductByCode(orderEntry.getSupplierCode());
                        somUoms = localSomClient.fetchSOMUomsByUomId(Optional.ofNullable(somProduct).map(SOMProduct::getDefaultUomIdValue).orElse("default_uomid"));
                    }
                    return OrderCreationRequest.OrderLine.builder()
                        .ttrOrderlinetype(ORDER_LINE_TYPE_NORMAL)
                        .ttrCopsbundlenumber(null)
                        .ttrWriteinproduct(false)
                        .ttrProduct(OrderCreationRequest.OrderLine.Product.builder()
                            .productid(Optional.ofNullable(somProduct).map(SOMProduct::getProductId).orElse(null))
                            .productnumber(Optional.ofNullable(somProduct).map(SOMProduct::getProductNumber).orElse(null))
                            .build())
                        .ttrProductnameeng(null)
                        .ttrProductnamechi(null)
                        .ttrUnit(OrderCreationRequest.OrderLine.Unit.builder()
                            .uomid(Optional.ofNullable(somUoms).map(SOMUoms::getUomId).orElse(null))
                            .name(Optional.ofNullable(somUoms).map(SOMUoms::getName).orElse(null))
                            .build())
                        .ttrPriceperunit(price)
                        .ttrQuantity(Double.valueOf(orderEntry.getQuantity()))
                        .ttrHoldqty(0.0)
                        .ttrBottlereturnqty(0.0)
                        .ttrPromotion(null)
                        .ttrContract(null)
                        .ttrReason(null)
                        .ttrSequence((double) sequenceCounter.incrementAndGet())
                        .ttrEcoupontype(null)
                        .ttrInvoiceprice(price)
                        .ttrRemainder(false)
                        .ttrStandardamount(orderEntry.getTotalPrice().doubleValue())
                        .ttrStandardprice(price)
                        .subOrderLines(null)
                        .build();
                }).toList())
        .build();
    }

    public boolean createOrderInSOM(int retry, String consCode, Order order, String orderCreationPayload) {
        try {
            var now = LocalDateTime.now();
            orderCreationRequestLogRepository.save(
                OrderCreationRequestLog.builder()
                    .consCode(consCode)
                    .orderId(order.getId())
                    .url("https://dynamic365.com/order/create")
                    .requestBody(orderCreationPayload)
                    .createDate(now)
                    .modifiedDate(now)
                    .httpStatusCode("200")
                    .responseBody("{}")
                    .message("Success")
                    .retryCount(retry)
                    .build());

            log.info("Successfully created order {} in SOM", order.getId());

            order.setIsCreatedInSOM(true);
            orderRepository.save(order);

        } catch (Throwable throwable) {
            log.error(throwable.getMessage());
            throwable.printStackTrace();
            return false;
        }
        return true;
    }

    private void handleRetryOrder(Order order, Order existingOrder) {
        BeanUtils.copyProperties(order, existingOrder, "id", "createdOn");
        var createdOrder = createOrder(existingOrder);
        templateService.saveCustomerTemplate(createdOrder);
        updateMiddlewareUser(createdOrder);
    }

    public Order createOrder(Order order) {
        log.info("Saving order {}", order.getId());
        Optional.ofNullable(order.getConsignments())
            .ifPresent(consignments -> consignments.forEach(consignment -> {
                consignment.setOrder(order);
                Optional.ofNullable(consignment.getOrderEntries())
                    .ifPresent(entries -> entries.forEach(entry -> entry.setConsignment(consignment)));
            }));
        order.setIsCreatedInSOM(false);
        var result = orderRepository.save(order);
        log.info("Saved order {}", order.getId());
        return result;
    }

    public void updateMiddlewareUser(Order order) {
        var orderUser = order.getUser();
        var optionalUser = userRepository.findByAccountNo(order.getUser().getAccountNo());
        if (optionalUser.isEmpty()) {
            log.info("Create new user with account no {}", orderUser.getAccountNo());
            userRepository.save(User.builder()
                .code(orderUser.getCode())
                .accountNo(orderUser.getAccountNo())
                .cardNumber(orderUser.getCardNumber())
                .description(orderUser.getDescription())
                .fiscalCode(orderUser.getFiscalCode())
                .loyaltyCardId(orderUser.getLoyaltyCardId())
                .vatCode(orderUser.getVatCode())
                .build());
            return;
        }
        log.info("Update existing user with account no {}", orderUser.getAccountNo());
        var updateUser = getUpdatingUser(optionalUser, orderUser);
        userRepository.save(updateUser);
    }

    private static User getUpdatingUser(Optional<User> optionalUser, User orderUser) {
        var updateUser = optionalUser.get();
        if (orderUser.getCode() != null && !orderUser.getCode().isEmpty()) {
            updateUser.setCode(orderUser.getCode());
        }
        updateUser.setCardNumber(orderUser.getCardNumber());
        updateUser.setDescription(orderUser.getDescription());
        updateUser.setDescription(orderUser.getFiscalCode());
        updateUser.setLoyaltyCardId(orderUser.getLoyaltyCardId());
        updateUser.setVatCode(orderUser.getVatCode());
        return updateUser;
    }

    private String getNewFileName(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            throw new IllegalArgumentException("The specified file does not exist or is not a valid file.");
        }

        // Extract the file extension
        String fileName = file.getName();
        int dotIndex = fileName.lastIndexOf('.');
        String extension = (dotIndex != -1) ? fileName.substring(dotIndex) : "";
        String oldName = (dotIndex != -1) ? fileName.substring(0, dotIndex) : fileName;

        // Return the new file name with the same extension
        return oldName + formatDateTime(Instant.now(), "_YYYYMMDD-HHmmSS") + extension;
    }
}
