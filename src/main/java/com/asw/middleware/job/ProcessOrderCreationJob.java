package com.asw.middleware.job;

import com.asw.middleware.repository.middleware.OrderRepository;
import com.asw.middleware.service.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class ProcessOrderCreationJob {

    private final OrderRepository orderRepository;
    private final OrderService orderService;

    @Scheduled(fixedDelay = 3000)
    public void process() {
        log.info("Check order creation");
        var notCreatedOrders = orderRepository.findByIsCreatedInSOMFalse();

        // Fetch OrderEntries in a separate query to avoid MultipleBagFetchException
        if (!notCreatedOrders.isEmpty()) {
            orderRepository.fetchOrderEntriesForOrders(notCreatedOrders);
        }

        notCreatedOrders.forEach(order -> {
            var customerTemplate = order.getCustomerTemplate();
            if (!StringUtils.isEmpty(customerTemplate.getSomCustomerId())) {
                orderService.createSOMOrder(order);
            }
        });
    }

}
