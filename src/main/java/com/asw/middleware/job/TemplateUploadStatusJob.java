package com.asw.middleware.job;

import com.asw.middleware.enums.CustomerTemplateStatus;
import com.asw.middleware.model.middleware.CustomerTemplate;
import com.asw.middleware.repository.middleware.CustomerTemplateRepository;
import com.asw.middleware.service.TemplateService;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
public class TemplateUploadStatusJob {

    private final CustomerTemplateRepository customerTemplateRepository;
    private final TemplateService templateService;

    @Transactional
    @Scheduled(fixedDelayString = "${asw.template.check-interval:5000}") // Default 5 minutes
    public void checkUploadStatus() {
        log.info("Checking upload status for templates");

        // Get templates with DOWNLOADED, PENDING and <PERSON><PERSON><PERSON>ABLE_FOR_RETRY status
        List<CustomerTemplate> templates = customerTemplateRepository.findAllByStatusInAndIsPrimaryAddressTrue(
            List.of(CustomerTemplateStatus.DOWNLOADED, CustomerTemplateStatus.PENDING, CustomerTemplateStatus.AVAILABLE_FOR_RETRY));

        if (templates.isEmpty()) {
            log.info("No templates to check upload status");
            return;
        }

        log.info("Checking upload status for {} templates", templates.size());
        List<CustomerTemplate> templatesToSave = new ArrayList<>();

        for (CustomerTemplate template : templates) {
            try {
                if (templateService.checkAndUpdateTemplateStatus(template)) {
                    templatesToSave.add(template);
                }
            } catch (Exception e) {
                log.error("Error checking template status for account {}: {}", template.getScAccountNo(), e.getMessage());
            }
        }

        if (!templatesToSave.isEmpty()) {
            // Get latest CustomerTemplate records based on scUserCode from templatesToSave
            Set<String> scUserCodes = templatesToSave.stream()
                .map(CustomerTemplate::getScUserCode)
                .filter(scUserCode -> scUserCode != null && !scUserCode.trim().isEmpty())
                .collect(Collectors.toSet());

            log.info("Found {} unique scUserCodes to fetch latest templates for", scUserCodes.size());

            if (!scUserCodes.isEmpty()) {
                // Fetch all latest templates for scUserCodes in a single query and add non-duplicates
                Set<String> existingTemplateIds = templatesToSave.stream()
                    .map(CustomerTemplate::getId)
                    .collect(Collectors.toSet());

                // Create map of scUserCode to processed template for SOM data copying
                Map<String, CustomerTemplate> processedTemplateMap = templatesToSave.stream()
                    .collect(Collectors.toMap(CustomerTemplate::getScUserCode, template -> template, (existing, replacement) -> existing));

                List<CustomerTemplate> newTemplates = customerTemplateRepository.findLatestByScUserCodes(scUserCodes)
                    .stream()
                    .filter(template -> !existingTemplateIds.contains(template.getId()))
                    .map(template -> {
                        // Copy SOM data from processed template with same scUserCode
                        CustomerTemplate processedTemplate = processedTemplateMap.get(template.getScUserCode());
                        if (processedTemplate != null) {
                            template.setSomCustomerId(processedTemplate.getSomCustomerId());
                            template.setSomAccountNo(processedTemplate.getSomAccountNo());
                        }
                        return template;
                    })
                    .toList();

                templatesToSave.addAll(newTemplates);
                log.info("Added {} new latest templates to save list", newTemplates.size());
            }

            log.info("Total templates to save: {}", templatesToSave.size());
            customerTemplateRepository.saveAll(templatesToSave);
        }
    }
}