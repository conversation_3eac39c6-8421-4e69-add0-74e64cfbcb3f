package com.asw.middleware.job;

import com.asw.middleware.enums.CustomerTemplateStatus;
import com.asw.middleware.model.middleware.CustomerTemplate;
import com.asw.middleware.repository.middleware.CustomerTemplateRepository;
import com.asw.middleware.service.TemplateService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
public class TemplateUploadStatusJob {

    private final CustomerTemplateRepository customerTemplateRepository;
    private final TemplateService templateService;

    @Transactional
    @Scheduled(fixedDelayString = "${asw.template.check-interval:5000}") // Default 5 minutes
    public void checkUploadStatus() {
        log.info("Checking upload status for templates");

        // Get and process templates in one step
        List<CustomerTemplate> templates = customerTemplateRepository.findAllByStatusInAndIsPrimaryAddressTrue(
            List.of(CustomerTemplateStatus.DOWNLOADED, CustomerTemplateStatus.PENDING, CustomerTemplateStatus.AVAILABLE_FOR_RETRY));

        if (templates.isEmpty()) {
            log.info("No templates to check upload status");
            return;
        }

        log.info("Checking upload status for {} templates", templates.size());

        // Process templates and collect valid ones with their scUserCodes in one stream
        Map<String, CustomerTemplate> processedTemplateMap = templates.stream()
            .filter(template -> {
                try {
                    return templateService.checkAndUpdateTemplateStatus(template);
                } catch (Exception e) {
                    log.error("Error checking template status for account {}: {}", template.getScAccountNo(), e.getMessage());
                    return false;
                }
            })
            .filter(template -> StringUtils.isNotBlank(template.getScUserCode()))
            .collect(Collectors.toMap(
                CustomerTemplate::getScUserCode,
                template -> template, (existing, replacement) -> existing)
            );

        if (processedTemplateMap.isEmpty()) {
            log.info("No valid templates to save");
            return;
        }

        List<CustomerTemplate> templatesToSave = new ArrayList<>(processedTemplateMap.values());
        Set<String> existingTemplateIds = templatesToSave.stream().map(CustomerTemplate::getId).collect(Collectors.toSet());

        log.info("Found {} unique scUserCodes to fetch latest templates for", processedTemplateMap.size());

        // Fetch latest templates and add non-duplicates with SOM data copying
        List<CustomerTemplate> newTemplates = customerTemplateRepository.findLatestByScUserCodes(processedTemplateMap.keySet())
            .stream()
            .filter(template -> !existingTemplateIds.contains(template.getId()))
            .peek(template -> {
                // Copy SOM data from processed template with same scUserCode
                CustomerTemplate processedTemplate = processedTemplateMap.get(template.getScUserCode());
                if (processedTemplate != null) {
                    template.setSomCustomerId(processedTemplate.getSomCustomerId());
                    template.setSomAccountNo(processedTemplate.getSomAccountNo());
                }
            })
            .toList();

        templatesToSave.addAll(newTemplates);
        log.info("Added {} new latest templates. Total templates to save: {}", newTemplates.size(), templatesToSave.size());

        customerTemplateRepository.saveAll(templatesToSave);
    }
}