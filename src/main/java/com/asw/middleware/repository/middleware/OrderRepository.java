package com.asw.middleware.repository.middleware;

import com.asw.middleware.model.middleware.Consignment;
import com.asw.middleware.model.middleware.Order;
import java.util.List;
import java.util.Set;

import com.asw.middleware.model.middleware.CustomerTemplate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

public interface OrderRepository extends JpaRepository<Order, String> {


    /**
     * Find all orders that have not been processed with optimized fetching
     *
     * @return List of unprocessed orders
     */
    @Query("SELECT DISTINCT o FROM Order o "
        + "LEFT JOIN FETCH o.shippingInfo "
        + "LEFT JOIN FETCH o.billingInfo "
        + "LEFT JOIN FETCH o.consignments c "
        + "LEFT JOIN FETCH c.orderEntries "
        + "WHERE o.processStatus != 'PROCESSED'"
    )
    List<Order> findUnprocessedWithDetails();

    @Query("SELECT DISTINCT o FROM Order o " +
           "LEFT JOIN FETCH o.consignments " +
           "LEFT JOIN FETCH o.customerTemplate " +
           "WHERE o.isCreatedInSOM = false")
    List<Order> findByIsCreatedInSOMFalse();

    /**
     * Fetch OrderEntries for the given orders to avoid MultipleBagFetchException
     * This is called after fetching orders with consignments
     */
    @Query("SELECT DISTINCT c FROM Consignment c " +
           "LEFT JOIN FETCH c.orderEntries " +
           "WHERE c.order IN :orders")
    List<Consignment> fetchOrderEntriesForOrders(@Param("orders") List<Order> orders);

    List<Order> findByCustomerTemplateAndIsCreatedInSOMFalse(CustomerTemplate customerTemplate);

    /**
     * Update processStatus to 'PROCESSED' for a list of order IDs
     *
     * @param orderIds List of order IDs to update
     * @return Number of rows affected
     */
    @Modifying
    @Transactional
    @Query("UPDATE Order o SET o.processStatus = 'PROCESSED' WHERE o.id IN :orderIds")
    int updateProcessStatusByOrderIds(@Param("orderIds") Set<String> orderIds);
}
