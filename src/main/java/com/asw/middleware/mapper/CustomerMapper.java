package com.asw.middleware.mapper;

import com.asw.middleware.dto.SOMCustomerMerging;
import com.asw.middleware.model.middleware.Order;
import com.asw.middleware.model.middleware.CustomerTemplate;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CustomerMapper {

//    @Mapping(target = "companyJson", ignore = true)
//    @Mapping(target = "marketingGroupRef", ignore = true)
//    @Mapping(target = "subMarketingGroupRef", ignore = true)
//    @Mapping(target = "buildingRef", ignore = true)
//    @Mapping(target = "streetRef", ignore = true)
//    @Mapping(target = "districtRef", ignore = true)
//    @Mapping(target = "areaRef", ignore = true)
//    SOMCustomerCreation toSOMCustomerCreation(CustomerTemplate customerTemplate);

    /**
     * Maps a CustomerTemplate to SOMCustomerMerging
     *
     * @param customerTemplate The CustomerTemplate to map
     * @return The mapped SOMCustomerMerging
     */
    @Mapping(target = "id", source = "somCustomerId")
    @Mapping(target = "lastName", source = "name")
    @Mapping(target = "buildingId", ignore = true)
    @Mapping(target = "streetId", ignore = true)
    @Mapping(target = "districtId", ignore = true)
    @Mapping(target = "areaId", ignore = true)
    @Mapping(target = "tradeClass", ignore = true)
    @Mapping(target = "companyCode", ignore = true)
    @Mapping(target = "marketingGroupId", ignore = true)
    @Mapping(target = "subMarketingGroupId", ignore = true)
    SOMCustomerMerging toSOMCustomerMerging(CustomerTemplate customerTemplate);

    /**
     * Custom implementation of toSOMCustomerMerging to use the setter methods in SOMCustomerMerging
     *
     * @param customerTemplate The CustomerTemplate to map
     * @return The mapped SOMCustomerMerging
     */
    default SOMCustomerMerging mapToSOMCustomerMerging(CustomerTemplate customerTemplate) {
        SOMCustomerMerging customerMerging = toSOMCustomerMerging(customerTemplate);

        // Use custom setters for reference fields
//        customerMerging.setMarketingGroupId(customerTemplate.getMarketingGroupId());
//        customerMerging.setSubMarketingGroupId(customerTemplate.getSubMarketingGroupId());
        customerMerging.setBuildingId(customerTemplate.getBuildingId());
        customerMerging.setStreetId(customerTemplate.getStreetId());
        customerMerging.setDistrictId(customerTemplate.getDistrictId());
        customerMerging.setAreaId(customerTemplate.getAreaId());

        return customerMerging;
    }

    /**
     * Merges data from source CustomerTemplate to target CustomerTemplate
     *
     * @param source The source CustomerTemplate containing the new data
     * @param target The target CustomerTemplate to be updated
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdOn", ignore = true)
    @Mapping(target = "status", ignore = true)
//    @Mapping(target = "lastRetryTime", ignore = true)
//    @Mapping(target = "firstRetryTime", ignore = true)
    @Mapping(target = "somCustomerId", ignore = true)
    @Mapping(target = "somAccountNo", ignore = true)
    @Mapping(target = "isAddressChanged", ignore = true)
    void mergeCustomerTemplate(CustomerTemplate source, @MappingTarget CustomerTemplate target);

    /**
     * Maps an Order to a new CustomerTemplate
     *
     * @param order The Order to map from
     * @return A new CustomerTemplate with basic information from the Order
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "scUserCode", source = "user.code")
    @Mapping(target = "scAccountNo", source = "user.accountNo")
    @Mapping(target = "email", source = "email")
    @Mapping(target = "firstName", source = "shippingInfo.firstName")
    @Mapping(target = "name", source = "shippingInfo.lastName")
    @Mapping(target = "longName", expression = "java(String.format(\"%s %s\", order.getShippingInfo().getFirstName(), order.getShippingInfo().getLastName()))")
    @Mapping(target = "district", source = "shippingInfo.district")
    @Mapping(target = "location", source = "shippingLocation")
    @Mapping(target = "phone", source = "shippingInfo.phone1")
    @Mapping(target = "modifiedOn", expression = "java(order.getUpdatedOn() != null ? order.getUpdatedOn().toString() : null)")
    @Mapping(target = "createdOn", ignore = true)
    @Mapping(target = "updatedOn", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "lastRetryTime", ignore = true)
    @Mapping(target = "firstRetryTime", ignore = true)
    CustomerTemplate orderToCustomerTemplate(Order order);
}
