package com.asw.middleware.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;

import com.asw.middleware.model.middleware.CustomerTemplate;
import com.asw.middleware.repository.middleware.CustomerTemplateRepository;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TemplateServiceTest {

    @Mock
    private CustomerTemplateRepository customerTemplateRepository;

    @Mock
    private TemplateConfigService templateConfigService;

    @InjectMocks
    private TemplateService templateService;

    private OffsetDateTime fromDate;
    private OffsetDateTime toDate;
    private CustomerTemplate customerTemplate;

    @BeforeEach
    void setUp() {
        // Initialize test data
        fromDate = OffsetDateTime.of(2023, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC);
        toDate = OffsetDateTime.of(2023, 12, 31, 23, 59, 59, 0, ZoneOffset.UTC);

        // Create sample CustomerTemplate
        customerTemplate = new CustomerTemplate();
        customerTemplate.setScAccountNo("USER001");
        customerTemplate.setName("Test User");
        customerTemplate.setEmail("<EMAIL>");
        customerTemplate.setCompanyCode("TEST");
        customerTemplate.setMarketingGroupId("MG001");
        customerTemplate.setSubMarketingGroupId("SMG001");
        customerTemplate.setCreatedOn(fromDate);
        customerTemplate.setUpdatedOn(fromDate.plusDays(1));
    }

}